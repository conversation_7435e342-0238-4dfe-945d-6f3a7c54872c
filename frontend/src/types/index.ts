export interface User {
  id: number;
  username: string;
  email: string;
  avatar_url?: string;
  is_online: boolean;
  is_active: boolean;
  created_at: string;
  last_seen?: string;
}

export interface Message {
  id: number;
  content: string;
  message_type: MessageType;
  status: MessageStatus;
  sender_id: number;
  recipient_id?: number;
  room_id?: number;
  created_at: string;
  updated_at: string;
  sender?: User;
  recipient?: User;
}

export interface Room {
  id: number;
  name: string;
  description?: string;
  room_type: RoomType;
  created_by: number;
  created_at: string;
  updated_at: string;
  member_count?: number;
  last_message?: Message;
}

export interface RoomMember {
  id: number;
  user_id: number;
  room_id: number;
  role: MemberRole;
  joined_at: string;
  user?: User;
}

export enum MessageType {
  DIRECT = 'direct',
  ROOM = 'room'
}

export enum MessageStatus {
  SENT = 'sent',
  DELIVERED = 'delivered',
  READ = 'read'
}

export enum RoomType {
  PUBLIC = 'public',
  PRIVATE = 'private'
}

export enum MemberRole {
  ADMIN = 'admin',
  MODERATOR = 'moderator',
  MEMBER = 'member'
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
}

export interface AuthResponse {
  access_token: string;
  token_type: string;
  user: User;
}

export interface WebSocketMessage {
  type: string;
  data: any;
}

export interface ChatMessage {
  content: string;
  recipient_id?: number;
  room_id?: number;
  message_type: MessageType;
}

export interface TypingData {
  user_id: number;
  username: string;
  room_id?: number;
  recipient_id?: number;
}

export interface Chat {
  id: string;
  type: 'direct' | 'room';
  name: string;
  avatar?: string;
  last_message?: Message;
  unread_count: number;
  is_online?: boolean;
  last_seen?: string;
  room?: Room;
  user?: User;
}

export interface ApiError {
  detail: string;
  status_code: number;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  size: number;
  pages: number;
}
