import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { 
  User, 
  Message, 
  Room, 
  LoginRequest, 
  RegisterRequest, 
  AuthResponse,
  PaginatedResponse,
  ApiError
} from '../types';

class ApiService {
  private api: AxiosInstance;
  private baseURL = 'http://localhost:8000/api/v1';

  constructor() {
    this.api = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('access_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor to handle errors
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          localStorage.removeItem('access_token');
          localStorage.removeItem('user');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // Auth endpoints
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    const response: AxiosResponse<AuthResponse> = await this.api.post('/auth/login', credentials);
    return response.data;
  }

  async register(userData: RegisterRequest): Promise<AuthResponse> {
    const response: AxiosResponse<AuthResponse> = await this.api.post('/auth/register', userData);
    return response.data;
  }

  async getCurrentUser(): Promise<User> {
    const response: AxiosResponse<User> = await this.api.get('/auth/me');
    return response.data;
  }

  // User endpoints
  async getUsers(page: number = 1, size: number = 50): Promise<PaginatedResponse<User>> {
    const response: AxiosResponse<PaginatedResponse<User>> = await this.api.get(
      `/users?page=${page}&size=${size}`
    );
    return response.data;
  }

  async getUserById(userId: number): Promise<User> {
    const response: AxiosResponse<User> = await this.api.get(`/users/${userId}`);
    return response.data;
  }

  async updateUser(userId: number, userData: Partial<User>): Promise<User> {
    const response: AxiosResponse<User> = await this.api.put(`/users/${userId}`, userData);
    return response.data;
  }

  // Message endpoints
  async getMessages(
    page: number = 1, 
    size: number = 50, 
    recipientId?: number, 
    roomId?: number
  ): Promise<PaginatedResponse<Message>> {
    let url = `/messages?page=${page}&size=${size}`;
    if (recipientId) url += `&recipient_id=${recipientId}`;
    if (roomId) url += `&room_id=${roomId}`;
    
    const response: AxiosResponse<PaginatedResponse<Message>> = await this.api.get(url);
    return response.data;
  }

  async sendMessage(messageData: {
    content: string;
    recipient_id?: number;
    room_id?: number;
    message_type: string;
  }): Promise<Message> {
    const response: AxiosResponse<Message> = await this.api.post('/messages', messageData);
    return response.data;
  }

  async markMessageAsRead(messageId: number): Promise<Message> {
    const response: AxiosResponse<Message> = await this.api.patch(
      `/messages/${messageId}`, 
      { status: 'read' }
    );
    return response.data;
  }

  // Room endpoints
  async getRooms(page: number = 1, size: number = 50): Promise<PaginatedResponse<Room>> {
    const response: AxiosResponse<PaginatedResponse<Room>> = await this.api.get(
      `/rooms?page=${page}&size=${size}`
    );
    return response.data;
  }

  async getRoomById(roomId: number): Promise<Room> {
    const response: AxiosResponse<Room> = await this.api.get(`/rooms/${roomId}`);
    return response.data;
  }

  async createRoom(roomData: {
    name: string;
    description?: string;
    room_type: string;
  }): Promise<Room> {
    const response: AxiosResponse<Room> = await this.api.post('/rooms', roomData);
    return response.data;
  }

  async joinRoom(roomId: number): Promise<void> {
    await this.api.post(`/rooms/${roomId}/join`);
  }

  async leaveRoom(roomId: number): Promise<void> {
    await this.api.post(`/rooms/${roomId}/leave`);
  }

  async getRoomMembers(roomId: number): Promise<User[]> {
    const response: AxiosResponse<User[]> = await this.api.get(`/rooms/${roomId}/members`);
    return response.data;
  }
}

export const apiService = new ApiService();
export default apiService;
