import { WebSocketMessage, Message, TypingData } from '../types';

type EventCallback = (data: any) => void;

class WebSocketService {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 3000;
  private eventListeners: Map<string, EventCallback[]> = new Map();
  private isConnecting = false;

  connect(token: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        resolve();
        return;
      }

      if (this.isConnecting) {
        reject(new Error('Already connecting'));
        return;
      }

      this.isConnecting = true;
      const wsUrl = `ws://localhost:8000/ws?token=${encodeURIComponent(token)}`;
      
      try {
        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
          console.log('WebSocket connected');
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          this.emit('connected', null);
          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data);
            this.handleMessage(message);
          } catch (error) {
            console.error('Error parsing WebSocket message:', error);
          }
        };

        this.ws.onclose = (event) => {
          console.log('WebSocket disconnected:', event.code, event.reason);
          this.isConnecting = false;
          this.emit('disconnected', { code: event.code, reason: event.reason });
          
          if (!event.wasClean && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect(token);
          }
        };

        this.ws.onerror = (error) => {
          console.error('WebSocket error:', error);
          this.isConnecting = false;
          this.emit('error', error);
          reject(error);
        };

      } catch (error) {
        this.isConnecting = false;
        reject(error);
      }
    });
  }

  private scheduleReconnect(token: string) {
    this.reconnectAttempts++;
    console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
    
    setTimeout(() => {
      this.connect(token).catch(console.error);
    }, this.reconnectInterval);
  }

  private handleMessage(message: WebSocketMessage) {
    console.log('Received WebSocket message:', message);
    
    switch (message.type) {
      case 'message':
        this.emit('message', message.data);
        break;
      case 'user_status':
        this.emit('userStatus', message.data);
        break;
      case 'typing':
        this.emit('typing', message.data);
        break;
      case 'message_read':
        this.emit('messageRead', message.data);
        break;
      case 'room_joined':
        this.emit('roomJoined', message.data);
        break;
      case 'room_left':
        this.emit('roomLeft', message.data);
        break;
      default:
        console.log('Unknown message type:', message.type);
    }
  }

  sendMessage(content: string, recipientId?: number, roomId?: number) {
    if (!this.isConnected()) {
      throw new Error('WebSocket not connected');
    }

    const message: WebSocketMessage = {
      type: 'chat_message',
      data: {
        content,
        recipient_id: recipientId,
        room_id: roomId,
        message_type: roomId ? 'room' : 'direct'
      }
    };

    this.send(message);
  }

  sendTyping(recipientId?: number, roomId?: number) {
    if (!this.isConnected()) return;

    const message: WebSocketMessage = {
      type: 'typing',
      data: {
        recipient_id: recipientId,
        room_id: roomId
      }
    };

    this.send(message);
  }

  markMessageAsRead(messageId: number) {
    if (!this.isConnected()) return;

    const message: WebSocketMessage = {
      type: 'message_read',
      data: {
        message_id: messageId
      }
    };

    this.send(message);
  }

  joinRoom(roomId: number) {
    if (!this.isConnected()) return;

    const message: WebSocketMessage = {
      type: 'join_room',
      data: {
        room_id: roomId
      }
    };

    this.send(message);
  }

  leaveRoom(roomId: number) {
    if (!this.isConnected()) return;

    const message: WebSocketMessage = {
      type: 'leave_room',
      data: {
        room_id: roomId
      }
    };

    this.send(message);
  }

  private send(message: WebSocketMessage) {
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    }
  }

  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.eventListeners.clear();
  }

  // Event system
  on(event: string, callback: EventCallback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  off(event: string, callback: EventCallback) {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  private emit(event: string, data: any) {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => callback(data));
    }
  }
}

export const websocketService = new WebSocketService();
export default websocketService;
