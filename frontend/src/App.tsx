import React, { useState } from 'react';
import Login from './pages/Login';
import Register from './pages/Register';

function App() {
  const [currentPage, setCurrentPage] = useState('login');
  const [user, setUser] = useState(null);

  // Simple login function
  const handleLogin = async (credentials) => {
    try {
      const response = await fetch('http://localhost:8000/api/v1/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      });

      if (response.ok) {
        const data = await response.json();
        localStorage.setItem('access_token', data.access_token);

        // Get user info
        const userResponse = await fetch('http://localhost:8000/api/v1/auth/me', {
          headers: {
            'Authorization': `Bearer ${data.access_token}`,
          },
        });

        if (userResponse.ok) {
          const userData = await userResponse.json();
          setUser(userData);
        }
      } else {
        const error = await response.json();
        throw new Error(error.detail || 'Login failed');
      }
    } catch (error) {
      throw error;
    }
  };

  // Simple register function
  const handleRegister = async (userData) => {
    try {
      const response = await fetch('http://localhost:8000/api/v1/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });

      if (response.ok) {
        // Auto login after registration
        await handleLogin({ username: userData.username, password: userData.password });
      } else {
        const error = await response.json();
        throw new Error(error.detail || 'Registration failed');
      }
    } catch (error) {
      throw error;
    }
  };

  // If user is logged in, show chat interface
  if (user) {
    return (
      <div className="flex h-screen bg-gray-100">
        {/* Simple chat interface */}
        <div className="flex-1 flex flex-col">
          <div className="bg-whatsapp-teal text-white p-4">
            <h1 className="text-xl font-bold">مرحباً {user.username}! 🎉</h1>
            <p className="text-sm opacity-75">تم تسجيل الدخول بنجاح</p>
          </div>

          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <div className="text-6xl mb-4">💬</div>
              <h2 className="text-2xl font-bold text-gray-800 mb-4">تايم شات</h2>
              <p className="text-gray-600 mb-6">مرحباً بك في تطبيق الدردشة!</p>
              <button
                onClick={() => {
                  localStorage.removeItem('access_token');
                  setUser(null);
                }}
                className="bg-red-500 hover:bg-red-600 text-white px-6 py-2 rounded-lg transition-colors"
              >
                تسجيل الخروج
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show login or register page
  return (
    <div>
      {currentPage === 'login' ? (
        <Login
          onLogin={handleLogin}
          onSwitchToRegister={() => setCurrentPage('register')}
        />
      ) : (
        <Register
          onRegister={handleRegister}
          onSwitchToLogin={() => setCurrentPage('login')}
        />
      )}
    </div>
  );
}

export default App;
