import React, { useState, useRef, useEffect } from 'react';
import { Send, Paperclip, Smile, Mic } from 'lucide-react';
import { useChat } from '../../context/ChatContext';

const MessageInput: React.FC = () => {
  const { sendMessage, sendTyping } = useChat();
  const [message, setMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    adjustTextareaHeight();
  }, [message]);

  const adjustTextareaHeight = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMessage(e.target.value);
    
    // Handle typing indicator
    if (!isTyping && e.target.value.trim()) {
      setIsTyping(true);
      sendTyping();
    }
    
    // Clear previous timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
    
    // Set new timeout to stop typing indicator
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);
    }, 1000);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const trimmedMessage = message.trim();
    if (!trimmedMessage) return;

    try {
      await sendMessage(trimmedMessage);
      setMessage('');
      setIsTyping(false);
      
      // Clear typing timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  return (
    <div className="bg-white border-t border-gray-200 p-4">
      <form onSubmit={handleSubmit} className="flex items-end space-x-3">
        {/* Attachment Button */}
        <button
          type="button"
          className="p-2 text-gray-500 hover:text-whatsapp-green transition-colors"
        >
          <Paperclip className="w-5 h-5" />
        </button>

        {/* Message Input Container */}
        <div className="flex-1 relative">
          <div className="flex items-end bg-gray-50 rounded-2xl border border-gray-200 focus-within:border-whatsapp-green transition-colors">
            {/* Emoji Button */}
            <button
              type="button"
              className="p-3 text-gray-500 hover:text-whatsapp-green transition-colors"
            >
              <Smile className="w-5 h-5" />
            </button>

            {/* Text Input */}
            <textarea
              ref={textareaRef}
              value={message}
              onChange={handleInputChange}
              onKeyPress={handleKeyPress}
              placeholder="اكتب رسالة..."
              className="flex-1 bg-transparent border-none outline-none resize-none py-3 px-2 max-h-32 min-h-[24px] text-gray-900 placeholder-gray-500"
              rows={1}
            />

            {/* Voice Message Button (when no text) */}
            {!message.trim() && (
              <button
                type="button"
                className="p-3 text-gray-500 hover:text-whatsapp-green transition-colors"
              >
                <Mic className="w-5 h-5" />
              </button>
            )}
          </div>
        </div>

        {/* Send Button */}
        {message.trim() && (
          <button
            type="submit"
            className="p-3 bg-whatsapp-green hover:bg-whatsapp-green-dark text-white rounded-full transition-colors"
          >
            <Send className="w-5 h-5" />
          </button>
        )}
      </form>
    </div>
  );
};

export default MessageInput;
