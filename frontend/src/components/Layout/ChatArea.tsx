import React from 'react';
import { useChat } from '../../context/ChatContext';
import ChatHeader from './ChatHeader';
import MessageList from './MessageList';
import MessageInput from './MessageInput';

const ChatArea: React.FC = () => {
  const { activeChat } = useChat();

  if (!activeChat) {
    return null;
  }

  return (
    <div className="flex flex-col h-full">
      {/* Chat Header */}
      <ChatHeader chat={activeChat} />
      
      {/* Messages Area */}
      <div className="flex-1 overflow-hidden">
        <MessageList />
      </div>
      
      {/* Message Input */}
      <MessageInput />
    </div>
  );
};

export default ChatArea;
