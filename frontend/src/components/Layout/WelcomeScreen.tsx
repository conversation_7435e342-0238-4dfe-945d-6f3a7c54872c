import React from 'react';
import { MessageCircle, Users, Zap, Shield } from 'lucide-react';

const WelcomeScreen: React.FC = () => {
  return (
    <div className="flex-1 chat-bg flex items-center justify-center" style={{background: 'linear-gradient(135deg, var(--whatsapp-gray), var(--gray-100))'}}>
      <div className="text-center max-w-md p-8" style={{margin: '0 auto'}}>
        {/* Logo */}
        <div className="flex justify-center mb-8">
          <div className="bg-whatsapp-green rounded-full p-6 shadow-lg">
            <MessageCircle className="w-16 h-16 text-white" />
          </div>
        </div>

        {/* Welcome Text */}
        <h1 className="text-3xl font-bold text-gray-800 mb-4">
          مرحباً بك في تايم شات
        </h1>
        <p className="text-gray-600 mb-8 leading-relaxed">
          ابدأ محادثة جديدة مع أصدقائك أو انضم إلى مجموعة للتواصل مع الآخرين.
          اختر محادثة من القائمة الجانبية للبدء.
        </p>

        {/* Features */}
        <div className="mb-8" style={{display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1.5rem'}}>
          <div className="text-center">
            <div className="bg-white rounded-lg p-4 shadow-sm mb-3">
              <Zap className="w-8 h-8 text-whatsapp-green mx-auto" />
            </div>
            <h3 className="font-semibold text-gray-800 mb-1">رسائل فورية</h3>
            <p className="text-sm text-gray-600">تواصل مع أصدقائك في الوقت الفعلي</p>
          </div>

          <div className="text-center">
            <div className="bg-white rounded-lg p-4 shadow-sm mb-3">
              <Users className="w-8 h-8 text-whatsapp-green mx-auto" />
            </div>
            <h3 className="font-semibold text-gray-800 mb-1">مجموعات</h3>
            <p className="text-sm text-gray-600">انضم إلى المجموعات وتواصل مع عدة أشخاص</p>
          </div>

          <div className="text-center">
            <div className="bg-white rounded-lg p-4 shadow-sm mb-3">
              <Shield className="w-8 h-8 text-whatsapp-green mx-auto" />
            </div>
            <h3 className="font-semibold text-gray-800 mb-1">آمن ومحمي</h3>
            <p className="text-sm text-gray-600">محادثاتك محمية ومشفرة بالكامل</p>
          </div>
        </div>

        {/* Call to Action */}
        <div className="bg-white rounded-lg p-6 shadow-sm">
          <h3 className="font-semibold text-gray-800 mb-2">ابدأ الآن</h3>
          <p className="text-gray-600 text-sm">
            اختر محادثة من القائمة الجانبية أو ابحث عن صديق جديد للبدء في المحادثة
          </p>
        </div>
      </div>
    </div>
  );
};

export default WelcomeScreen;
