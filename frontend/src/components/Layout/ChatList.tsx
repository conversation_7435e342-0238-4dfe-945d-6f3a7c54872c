import React from 'react';
import { Check, Check<PERSON>he<PERSON>, <PERSON>, Circle } from 'lucide-react';
import { Chat } from '../../types';
import { useChat } from '../../context/ChatContext';
import { useAuth } from '../../context/AuthContext';

interface ChatListProps {
  chats: Chat[];
}

const ChatList: React.FC<ChatListProps> = ({ chats }) => {
  const { activeChat, setActiveChat } = useChat();
  const { user } = useAuth();

  const formatTime = (dateString?: string) => {
    if (!dateString) return '';
    
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      return date.toLocaleTimeString('ar-SA', { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: false 
      });
    } else if (diffInHours < 168) { // Less than a week
      return date.toLocaleDateString('ar-SA', { weekday: 'short' });
    } else {
      return date.toLocaleDateString('ar-SA', { 
        day: '2-digit', 
        month: '2-digit' 
      });
    }
  };

  const getMessageStatus = (message: any) => {
    if (!message || message.sender_id !== user?.id) return null;
    
    switch (message.status) {
      case 'sent':
        return <Check className="w-4 h-4 text-gray-400" />;
      case 'delivered':
        return <CheckCheck className="w-4 h-4 text-gray-400" />;
      case 'read':
        return <CheckCheck className="w-4 h-4 text-whatsapp-blue" />;
      default:
        return null;
    }
  };

  const truncateMessage = (content: string, maxLength: number = 50) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  if (chats.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-gray-500 p-8">
        <Users className="w-12 h-12 mb-4 opacity-50" />
        <p className="text-center">لا توجد محادثات بعد</p>
        <p className="text-sm text-center mt-2">ابدأ محادثة جديدة مع أصدقائك</p>
      </div>
    );
  }

  return (
    <div className="divide-y divide-gray-100">
      {chats.map((chat) => (
        <div
          key={chat.id}
          onClick={() => setActiveChat(chat)}
          className={`p-4 cursor-pointer transition-colors hover:bg-gray-50 ${
            activeChat?.id === chat.id ? 'bg-whatsapp-green-light' : ''
          }`}
        >
          <div className="flex items-center space-x-3">
            {/* Avatar */}
            <div className="relative">
              <div className="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center overflow-hidden">
                {chat.avatar ? (
                  <img 
                    src={chat.avatar} 
                    alt={chat.name}
                    className="w-full h-full object-cover"
                  />
                ) : chat.type === 'room' ? (
                  <Users className="w-6 h-6 text-gray-600" />
                ) : (
                  <span className="text-gray-600 font-semibold">
                    {chat.name.charAt(0).toUpperCase()}
                  </span>
                )}
              </div>
              
              {/* Online indicator for direct chats */}
              {chat.type === 'direct' && chat.is_online && (
                <div className="absolute bottom-0 left-0 w-3 h-3 bg-whatsapp-green rounded-full border-2 border-white"></div>
              )}
            </div>

            {/* Chat Info */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold text-gray-900 truncate">
                  {chat.name}
                </h3>
                <div className="flex items-center space-x-1">
                  {chat.last_message && getMessageStatus(chat.last_message)}
                  <span className="text-xs text-gray-500">
                    {formatTime(chat.last_message?.created_at)}
                  </span>
                </div>
              </div>
              
              <div className="flex items-center justify-between mt-1">
                <p className="text-sm text-gray-600 truncate">
                  {chat.last_message ? (
                    <>
                      {chat.last_message.sender_id === user?.id && 'أنت: '}
                      {truncateMessage(chat.last_message.content)}
                    </>
                  ) : chat.type === 'direct' ? (
                    chat.is_online ? 'متصل' : `آخر ظهور ${formatTime(chat.last_seen)}`
                  ) : (
                    'لا توجد رسائل'
                  )}
                </p>
                
                {/* Unread count */}
                {chat.unread_count > 0 && (
                  <div className="bg-whatsapp-green text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                    {chat.unread_count > 99 ? '99+' : chat.unread_count}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ChatList;
