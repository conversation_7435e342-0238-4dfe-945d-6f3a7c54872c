import React, { useEffect, useRef } from 'react';
import { Check, CheckCheck } from 'lucide-react';
import { useChat } from '../../context/ChatContext';
import { useAuth } from '../../context/AuthContext';
import { Message } from '../../types';

const MessageList: React.FC = () => {
  const { messages, typingUsers, activeChat } = useChat();
  const { user } = useAuth();
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('ar-SA', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    });
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return 'اليوم';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'أمس';
    } else {
      return date.toLocaleDateString('ar-SA', { 
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    }
  };

  const getMessageStatus = (message: Message) => {
    if (message.sender_id !== user?.id) return null;
    
    switch (message.status) {
      case 'sent':
        return <Check className="w-4 h-4 text-gray-400" />;
      case 'delivered':
        return <CheckCheck className="w-4 h-4 text-gray-400" />;
      case 'read':
        return <CheckCheck className="w-4 h-4 text-whatsapp-blue" />;
      default:
        return null;
    }
  };

  const shouldShowDateSeparator = (currentMessage: Message, previousMessage?: Message) => {
    if (!previousMessage) return true;
    
    const currentDate = new Date(currentMessage.created_at).toDateString();
    const previousDate = new Date(previousMessage.created_at).toDateString();
    
    return currentDate !== previousDate;
  };

  const getTypingUsersForCurrentChat = () => {
    if (!activeChat) return [];
    
    const [type, id] = activeChat.id.split('_');
    const numericId = parseInt(id);
    
    return typingUsers.filter(typing => {
      if (type === 'direct') {
        return typing.recipient_id === user?.id || typing.user_id === numericId;
      } else {
        return typing.room_id === numericId;
      }
    });
  };

  const currentTypingUsers = getTypingUsersForCurrentChat();

  return (
    <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-whatsapp-gray">
      {messages.map((message, index) => {
        const isOwnMessage = message.sender_id === user?.id;
        const previousMessage = index > 0 ? messages[index - 1] : undefined;
        const showDateSeparator = shouldShowDateSeparator(message, previousMessage);

        return (
          <div key={message.id}>
            {/* Date Separator */}
            {showDateSeparator && (
              <div className="flex justify-center my-4">
                <div className="bg-white px-3 py-1 rounded-lg shadow-sm text-xs text-gray-600">
                  {formatDate(message.created_at)}
                </div>
              </div>
            )}

            {/* Message */}
            <div
              className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'} message-slide-in`}
            >
              <div
                className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg shadow-sm ${
                  isOwnMessage
                    ? 'bg-whatsapp-green text-white'
                    : 'bg-white text-gray-900'
                }`}
              >
                {/* Sender name for group messages */}
                {!isOwnMessage && activeChat?.type === 'room' && (
                  <div className="text-xs font-semibold text-whatsapp-green mb-1">
                    {message.sender?.username || 'مستخدم'}
                  </div>
                )}

                {/* Message content */}
                <div className="break-words">{message.content}</div>

                {/* Time and status */}
                <div className={`flex items-center justify-end mt-1 space-x-1 ${
                  isOwnMessage ? 'text-white/70' : 'text-gray-500'
                }`}>
                  <span className="text-xs">{formatTime(message.created_at)}</span>
                  {getMessageStatus(message)}
                </div>
              </div>
            </div>
          </div>
        );
      })}

      {/* Typing Indicator */}
      {currentTypingUsers.length > 0 && (
        <div className="flex justify-start">
          <div className="bg-white px-4 py-2 rounded-lg shadow-sm max-w-xs">
            <div className="flex items-center space-x-2">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-gray-400 rounded-full typing-dot"></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full typing-dot"></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full typing-dot"></div>
              </div>
              <span className="text-sm text-gray-600">
                {currentTypingUsers.length === 1 
                  ? `${currentTypingUsers[0].username} يكتب...`
                  : `${currentTypingUsers.length} أشخاص يكتبون...`
                }
              </span>
            </div>
          </div>
        </div>
      )}

      <div ref={messagesEndRef} />
    </div>
  );
};

export default MessageList;
