import React, { useState } from 'react';
import { Phone, Video, MoreVertical, Users, Search, Circle } from 'lucide-react';
import { Chat } from '../../types';

interface ChatHeaderProps {
  chat: Chat;
}

const ChatHeader: React.FC<ChatHeaderProps> = ({ chat }) => {
  const [showMenu, setShowMenu] = useState(false);

  const getStatusText = () => {
    if (chat.type === 'room') {
      return `${chat.room?.member_count || 0} عضو`;
    }
    
    if (chat.is_online) {
      return 'متصل';
    }
    
    if (chat.last_seen) {
      const lastSeen = new Date(chat.last_seen);
      const now = new Date();
      const diffInMinutes = (now.getTime() - lastSeen.getTime()) / (1000 * 60);
      
      if (diffInMinutes < 60) {
        return `آخر ظهور منذ ${Math.floor(diffInMinutes)} دقيقة`;
      } else if (diffInMinutes < 1440) {
        return `آخر ظهور منذ ${Math.floor(diffInMinutes / 60)} ساعة`;
      } else {
        return `آخر ظهور ${lastSeen.toLocaleDateString('ar-SA')}`;
      }
    }
    
    return 'غير متصل';
  };

  return (
    <div className="bg-white border-b border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        {/* Chat Info */}
        <div className="flex items-center space-x-4">
          {/* Avatar */}
          <div className="relative">
            <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center overflow-hidden">
              {chat.avatar ? (
                <img 
                  src={chat.avatar} 
                  alt={chat.name}
                  className="w-full h-full object-cover"
                />
              ) : chat.type === 'room' ? (
                <Users className="w-5 h-5 text-gray-600" />
              ) : (
                <span className="text-gray-600 font-semibold">
                  {chat.name.charAt(0).toUpperCase()}
                </span>
              )}
            </div>
            
            {/* Online indicator for direct chats */}
            {chat.type === 'direct' && chat.is_online && (
              <div className="absolute bottom-0 left-0 w-3 h-3 bg-whatsapp-green rounded-full border-2 border-white"></div>
            )}
          </div>

          {/* Name and Status */}
          <div>
            <h2 className="font-semibold text-gray-900">{chat.name}</h2>
            <p className="text-sm text-gray-600 flex items-center">
              {chat.type === 'direct' && chat.is_online && (
                <Circle className="w-2 h-2 text-whatsapp-green fill-current mr-2" />
              )}
              {getStatusText()}
            </p>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center space-x-2">
          <button className="p-2 text-gray-600 hover:text-whatsapp-green hover:bg-gray-100 rounded-full transition-colors">
            <Search className="w-5 h-5" />
          </button>
          
          {chat.type === 'direct' && (
            <>
              <button className="p-2 text-gray-600 hover:text-whatsapp-green hover:bg-gray-100 rounded-full transition-colors">
                <Phone className="w-5 h-5" />
              </button>
              <button className="p-2 text-gray-600 hover:text-whatsapp-green hover:bg-gray-100 rounded-full transition-colors">
                <Video className="w-5 h-5" />
              </button>
            </>
          )}
          
          <div className="relative">
            <button
              onClick={() => setShowMenu(!showMenu)}
              className="p-2 text-gray-600 hover:text-whatsapp-green hover:bg-gray-100 rounded-full transition-colors"
            >
              <MoreVertical className="w-5 h-5" />
            </button>
            
            {showMenu && (
              <div className="absolute left-0 top-12 bg-white rounded-lg shadow-lg py-2 w-48 z-10 border">
                <button className="w-full text-right px-4 py-2 text-gray-700 hover:bg-gray-100">
                  عرض الملف الشخصي
                </button>
                <button className="w-full text-right px-4 py-2 text-gray-700 hover:bg-gray-100">
                  البحث في المحادثة
                </button>
                <button className="w-full text-right px-4 py-2 text-gray-700 hover:bg-gray-100">
                  كتم الإشعارات
                </button>
                {chat.type === 'room' && (
                  <>
                    <button className="w-full text-right px-4 py-2 text-gray-700 hover:bg-gray-100">
                      معلومات المجموعة
                    </button>
                    <button className="w-full text-right px-4 py-2 text-red-600 hover:bg-gray-100">
                      مغادرة المجموعة
                    </button>
                  </>
                )}
                {chat.type === 'direct' && (
                  <button className="w-full text-right px-4 py-2 text-red-600 hover:bg-gray-100">
                    حذف المحادثة
                  </button>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatHeader;
