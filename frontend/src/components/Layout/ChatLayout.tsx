import React from 'react';
import { useAuth } from '../../context/AuthContext';
import { useChat } from '../../context/ChatContext';
import Sidebar from './Sidebar';
import ChatArea from './ChatArea';
import WelcomeScreen from './WelcomeScreen';

const ChatLayout: React.FC = () => {
  const { user } = useAuth();
  const { activeChat } = useChat();

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <div className="w-1/3 min-w-[320px] max-w-[400px] bg-white border-r border-gray-200">
        <Sidebar />
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {activeChat ? (
          <ChatArea />
        ) : (
          <WelcomeScreen />
        )}
      </div>
    </div>
  );
};

export default ChatLayout;
