import React from 'react';
import { useAuth } from '../../context/AuthContext';
import { useChat } from '../../context/ChatContext';
import Sidebar from './Sidebar';
import ChatArea from './ChatArea';
import WelcomeScreen from './WelcomeScreen';

const ChatLayout: React.FC = () => {
  const { user } = useAuth();
  const { activeChat } = useChat();

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <div className="sidebar-width bg-white border-r">
        <Sidebar />
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {activeChat ? (
          <ChatArea />
        ) : (
          <WelcomeScreen />
        )}
      </div>
    </div>
  );
};

export default ChatLayout;
