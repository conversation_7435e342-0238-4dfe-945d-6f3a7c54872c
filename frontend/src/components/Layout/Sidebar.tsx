import React, { useState } from 'react';
import { Search, MoreVertical, MessageCircle, Users, Settings, LogOut } from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import { useChat } from '../../context/ChatContext';
import ChatList from './ChatList';

const Sidebar: React.FC = () => {
  const { user, logout } = useAuth();
  const { chats } = useChat();
  const [searchQuery, setSearchQuery] = useState('');
  const [showMenu, setShowMenu] = useState(false);

  const filteredChats = chats.filter(chat =>
    chat.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleLogout = () => {
    logout();
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="bg-whatsapp-teal text-white p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center">
              <span className="text-whatsapp-teal font-semibold">
                {user?.username.charAt(0).toUpperCase()}
              </span>
            </div>
            <div>
              <h2 className="font-semibold">{user?.username}</h2>
              <p className="text-xs opacity-75">متصل</p>
            </div>
          </div>
          
          <div className="relative">
            <button
              onClick={() => setShowMenu(!showMenu)}
              className="p-2 hover:bg-whatsapp-green-dark rounded-full transition-colors"
            >
              <MoreVertical className="w-5 h-5" />
            </button>
            
            {showMenu && (
              <div className="absolute left-0 top-12 bg-white rounded-lg shadow-lg py-2 w-48 z-10">
                <button className="w-full text-right px-4 py-2 text-gray-700 hover:bg-gray-100 flex items-center">
                  <Settings className="w-4 h-4 ml-3" />
                  الإعدادات
                </button>
                <button 
                  onClick={handleLogout}
                  className="w-full text-right px-4 py-2 text-red-600 hover:bg-gray-100 flex items-center"
                >
                  <LogOut className="w-4 h-4 ml-3" />
                  تسجيل الخروج
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Search */}
      <div className="p-4 bg-gray-50 border-b">
        <div className="relative">
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="البحث في المحادثات..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-4 pr-10 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-whatsapp-green"
          />
        </div>
      </div>

      {/* Chat List */}
      <div className="flex-1 overflow-y-auto">
        <ChatList chats={filteredChats} />
      </div>

      {/* Footer */}
      <div className="p-4 bg-gray-50 border-t">
        <div className="flex justify-center space-x-4">
          <button className="p-2 text-gray-600 hover:text-whatsapp-green transition-colors">
            <MessageCircle className="w-5 h-5" />
          </button>
          <button className="p-2 text-gray-600 hover:text-whatsapp-green transition-colors">
            <Users className="w-5 h-5" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
