import React from 'react';

const TestPage: React.FC = () => {
  return (
    <div style={{ 
      padding: '20px', 
      fontFamily: 'Arial, sans-serif',
      backgroundColor: '#f0f2f5',
      minHeight: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }}>
      <div style={{
        backgroundColor: 'white',
        padding: '40px',
        borderRadius: '10px',
        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
        textAlign: 'center',
        maxWidth: '400px'
      }}>
        <h1 style={{ color: '#25D366', marginBottom: '20px' }}>
          🎉 تايم شات يعمل!
        </h1>
        <p style={{ color: '#666', marginBottom: '20px' }}>
          تم إنشاء التطبيق بنجاح وهو جاهز للاستخدام
        </p>
        <div style={{
          backgroundColor: '#25D366',
          color: 'white',
          padding: '10px 20px',
          borderRadius: '5px',
          display: 'inline-block'
        }}>
          ✅ Frontend يعمل بشكل صحيح
        </div>
      </div>
    </div>
  );
};

export default TestPage;
