import React, { useState } from 'react';
import { <PERSON>Circle, Eye, EyeOff, Loader2 } from 'lucide-react';

interface LoginProps {
  onLogin: (credentials: { username: string; password: string }) => Promise<void>;
  onSwitchToRegister: () => void;
}

const Login: React.FC<LoginProps> = ({ onLogin, onSwitchToRegister }) => {
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
    setError('');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      await onLogin(formData);
    } catch (error: any) {
      setError(error.message || 'فشل في تسجيل الدخول');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDemoLogin = async (username: string, password: string) => {
    setFormData({ username, password });
    setIsLoading(true);
    setError('');

    try {
      await onLogin({ username, password });
    } catch (error: any) {
      setError(error.message || 'فشل في تسجيل الدخول');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4" style={{background: 'linear-gradient(135deg, var(--whatsapp-teal), var(--whatsapp-green-dark))'}}>
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-md p-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex justify-center mb-4">
            <div className="bg-whatsapp-green rounded-full p-3">
              <MessageCircle className="w-8 h-8 text-white" />
            </div>
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">مرحباً بك مرة أخرى</h1>
          <p className="text-gray-600 mb-4">سجل دخولك للمتابعة مع أصدقائك</p>

          {/* Demo accounts info */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 text-sm">
            <p className="font-medium text-blue-800 mb-3">حسابات تجريبية:</p>
            <div className="space-y-2">
              <button
                type="button"
                onClick={() => handleDemoLogin('ahmed', 'ahmed123')}
                disabled={isLoading}
                className="w-full text-left px-3 py-2 bg-blue-100 hover:bg-blue-200 text-blue-800 rounded transition-colors disabled:opacity-50"
              >
                تسجيل دخول كـ أحمد
              </button>
              <button
                type="button"
                onClick={() => handleDemoLogin('fatima', 'fatima123')}
                disabled={isLoading}
                className="w-full text-left px-3 py-2 bg-pink-100 hover:bg-pink-200 text-pink-800 rounded transition-colors disabled:opacity-50"
              >
                تسجيل دخول كـ فاطمة
              </button>
            </div>
          </div>
        </div>

        {/* Divider */}
        <div className="flex items-center my-6">
          <div className="flex-1 border-t border-gray-300"></div>
          <span className="px-4 text-gray-500 text-sm">أو</span>
          <div className="flex-1 border-t border-gray-300"></div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="px-4 py-3 rounded-lg mb-6" style={{backgroundColor: '#fef2f2', border: '1px solid #fecaca', color: '#dc2626'}}>
            {error}
          </div>
        )}

        {/* Login Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-2">
              اسم المستخدم
            </label>
            <input
              type="text"
              id="username"
              name="username"
              value={formData.username}
              onChange={handleChange}
              required
              className="w-full px-4 py-3 rounded-lg focus:outline-none focus:ring-2 transition-colors"
              placeholder="أدخل اسم المستخدم"
            />
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
              كلمة المرور
            </label>
            <div className="relative">
              <input
                type={showPassword ? 'text' : 'password'}
                id="password"
                name="password"
                value={formData.password}
                onChange={handleChange}
                required
                className="w-full px-4 py-3 rounded-lg focus:outline-none focus:ring-2 transition-colors"
                style={{paddingLeft: '3rem'}}
                placeholder="أدخل كلمة المرور"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
              >
                {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
              </button>
            </div>
          </div>

          <button
            type="submit"
            disabled={isLoading}
            className="w-full bg-whatsapp-green hover:bg-whatsapp-green-dark text-white font-medium py-3 px-4 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
          >
            {isLoading ? (
              <>
                <Loader2 className="w-5 h-5 animate-spin mr-2" />
                جاري تسجيل الدخول...
              </>
            ) : (
              'تسجيل الدخول'
            )}
          </button>
        </form>

        {/* Register Link */}
        <div className="mt-6 text-center">
          <p className="text-gray-600">
            ليس لديك حساب؟{' '}
            <button
              onClick={onSwitchToRegister}
              className="text-whatsapp-green font-medium transition-colors"
              style={{background: 'none', border: 'none', textDecoration: 'underline', cursor: 'pointer'}}
            >
              إنشاء حساب جديد
            </button>
          </p>
        </div>
      </div>
    </div>
  );
};

export default Login;
