import React, { useState } from 'react';
import { MessageCircle, Users, Search, Send, Phone, Video, MoreVertical } from 'lucide-react';

const Demo: React.FC = () => {
  const [activeTab, setActiveTab] = useState('login');

  const LoginDemo = () => (
    <div className="min-h-screen flex items-center justify-center p-4" style={{background: 'linear-gradient(135deg, var(--whatsapp-teal), var(--whatsapp-green-dark))'}}>
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-md p-8">
        <div className="text-center mb-8">
          <div className="flex justify-center mb-4">
            <div className="bg-whatsapp-green rounded-full p-3">
              <MessageCircle className="w-8 h-8 text-white" />
            </div>
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">مرحباً بك مرة أخرى</h1>
          <p className="text-gray-600">سجل دخولك للمتابعة مع أصدقائك</p>
        </div>

        <form className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">اسم المستخدم</label>
            <input
              type="text"
              className="w-full px-4 py-3 rounded-lg focus:outline-none focus:ring-2 transition-colors"
              placeholder="أدخل اسم المستخدم"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">كلمة المرور</label>
            <input
              type="password"
              className="w-full px-4 py-3 rounded-lg focus:outline-none focus:ring-2 transition-colors"
              placeholder="أدخل كلمة المرور"
            />
          </div>

          <button
            type="button"
            className="w-full bg-whatsapp-green hover:bg-whatsapp-green-dark text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center"
          >
            تسجيل الدخول
          </button>
        </form>
      </div>
    </div>
  );

  const ChatDemo = () => (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <div className="sidebar-width bg-white border-r">
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="bg-whatsapp-teal text-white p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center">
                  <span className="text-whatsapp-teal font-semibold">م</span>
                </div>
                <div>
                  <h2 className="font-semibold">محمد أحمد</h2>
                  <p className="text-xs opacity-75">متصل</p>
                </div>
              </div>
              <MoreVertical className="w-5 h-5" />
            </div>
          </div>

          {/* Search */}
          <div className="p-4 bg-gray-50 border-b">
            <div className="relative">
              <Search className="absolute w-4 h-4 text-gray-400" style={{right: '12px', top: '50%', transform: 'translateY(-50%)'}} />
              <input
                type="text"
                placeholder="البحث في المحادثات..."
                className="w-full py-2 bg-white rounded-lg focus:outline-none focus:ring-2"
                style={{paddingLeft: '1rem', paddingRight: '2.5rem'}}
              />
            </div>
          </div>

          {/* Chat List */}
          <div className="flex-1 overflow-y-auto">
            {[
              { name: 'أحمد محمد', message: 'كيف حالك؟', time: '10:30', unread: 2, online: true },
              { name: 'فاطمة علي', message: 'شكراً لك', time: '09:45', unread: 0, online: false },
              { name: 'مجموعة العمل', message: 'الاجتماع غداً', time: '08:20', unread: 5, online: false, isGroup: true },
            ].map((chat, index) => (
              <div key={index} className="p-4 cursor-pointer transition-colors hover:bg-gray-50 border-b">
                <div className="flex items-center space-x-3">
                  <div className="relative">
                    <div className="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center">
                      {chat.isGroup ? (
                        <Users className="w-6 h-6 text-gray-600" />
                      ) : (
                        <span className="text-gray-600 font-semibold">{chat.name.charAt(0)}</span>
                      )}
                    </div>
                    {chat.online && !chat.isGroup && (
                      <div className="absolute bottom-0 left-0 w-3 h-3 bg-whatsapp-green rounded-full border-2 border-white"></div>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <h3 className="font-semibold text-gray-900 truncate">{chat.name}</h3>
                      <span className="text-xs text-gray-500">{chat.time}</span>
                    </div>
                    <div className="flex items-center justify-between mt-1">
                      <p className="text-sm text-gray-600 truncate">{chat.message}</p>
                      {chat.unread > 0 && (
                        <div className="bg-whatsapp-green text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                          {chat.unread}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Chat Header */}
        <div className="bg-white border-b px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                <span className="text-gray-600 font-semibold">أ</span>
              </div>
              <div>
                <h2 className="font-semibold text-gray-900">أحمد محمد</h2>
                <p className="text-sm text-gray-600">متصل</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Phone className="w-5 h-5 text-gray-600" />
              <Video className="w-5 h-5 text-gray-600" />
              <MoreVertical className="w-5 h-5 text-gray-600" />
            </div>
          </div>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 chat-bg">
          <div className="space-y-4">
            <div className="flex justify-start">
              <div className="bg-white px-4 py-2 rounded-lg shadow-sm max-w-xs">
                <div>مرحباً! كيف حالك؟</div>
                <div className="text-xs text-gray-500 mt-1">10:25</div>
              </div>
            </div>
            <div className="flex justify-end">
              <div className="bg-whatsapp-green text-white px-4 py-2 rounded-lg shadow-sm max-w-xs">
                <div>الحمد لله، بخير. وأنت؟</div>
                <div className="text-xs text-white/70 mt-1">10:26</div>
              </div>
            </div>
          </div>
        </div>

        {/* Message Input */}
        <div className="bg-white border-t p-4">
          <div className="flex items-center space-x-3">
            <div className="flex-1 relative">
              <input
                type="text"
                placeholder="اكتب رسالة..."
                className="w-full px-4 py-3 bg-gray-50 rounded-2xl focus:outline-none focus:ring-2"
              />
            </div>
            <button className="p-3 bg-whatsapp-green text-white rounded-full">
              <Send className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div>
      {/* Navigation */}
      <div className="fixed top-4 left-4 z-50 bg-white rounded-lg shadow-lg p-2">
        <div className="flex space-x-2">
          <button
            onClick={() => setActiveTab('login')}
            className={`px-4 py-2 rounded ${activeTab === 'login' ? 'bg-whatsapp-green text-white' : 'text-gray-600'}`}
          >
            تسجيل الدخول
          </button>
          <button
            onClick={() => setActiveTab('chat')}
            className={`px-4 py-2 rounded ${activeTab === 'chat' ? 'bg-whatsapp-green text-white' : 'text-gray-600'}`}
          >
            الدردشة
          </button>
        </div>
      </div>

      {/* Content */}
      {activeTab === 'login' ? <LoginDemo /> : <ChatDemo />}
    </div>
  );
};

export default Demo;
