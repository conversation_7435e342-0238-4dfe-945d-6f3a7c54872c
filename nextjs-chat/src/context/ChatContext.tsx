'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { Message, Chat, User, TypingData, NotificationData, ChatState } from '@/types';
import { apiService } from '@/lib/api';
import { websocketService } from '@/lib/websocket';
import { useAuth } from './AuthContext';

interface ChatContextType extends ChatState {
  setActiveChat: (chat: Chat | null) => void;
  sendMessage: (content: string) => Promise<void>;
  loadMessages: (chatId: string) => Promise<void>;
  markAsRead: (messageId: number) => void;
  sendTyping: () => void;
  refreshChats: () => Promise<void>;
  clearNotifications: () => void;
  markNotificationAsRead: (notificationId: string) => void;
}

const ChatContext = createContext<ChatContextType | undefined>(undefined);

interface ChatProviderProps {
  children: ReactNode;
}

export const ChatProvider: React.FC<ChatProviderProps> = ({ children }) => {
  const { user, isAuthenticated } = useAuth();
  const [chats, setChats] = useState<Chat[]>([]);
  const [activeChat, setActiveChat] = useState<Chat | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [typingUsers, setTypingUsers] = useState<TypingData[]>([]);
  const [notifications, setNotifications] = useState<NotificationData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    if (isAuthenticated && user) {
      initializeChat();
      setupWebSocketListeners();
    }

    return () => {
      cleanupWebSocketListeners();
    };
  }, [isAuthenticated, user]);

  const initializeChat = async () => {
    await refreshChats();
  };

  const setupWebSocketListeners = () => {
    websocketService.on('connected', handleConnected);
    websocketService.on('disconnected', handleDisconnected);
    websocketService.on('message', handleNewMessage);
    websocketService.on('typing', handleTyping);
    websocketService.on('userStatus', handleUserStatus);
    websocketService.on('messageRead', handleMessageRead);
    websocketService.on('notification', handleNotification);
  };

  const cleanupWebSocketListeners = () => {
    websocketService.off('connected', handleConnected);
    websocketService.off('disconnected', handleDisconnected);
    websocketService.off('message', handleNewMessage);
    websocketService.off('typing', handleTyping);
    websocketService.off('userStatus', handleUserStatus);
    websocketService.off('messageRead', handleMessageRead);
    websocketService.off('notification', handleNotification);
  };

  const handleConnected = () => {
    setIsConnected(true);
  };

  const handleDisconnected = () => {
    setIsConnected(false);
  };

  const handleNewMessage = (message: Message) => {
    setMessages(prev => [...prev, message]);
    
    // Update chat list with new message
    setChats(prev => prev.map(chat => {
      if (
        (chat.type === 'direct' && 
         ((chat.user?.id === message.sender_id && message.recipient_id === user?.id) ||
          (chat.user?.id === message.recipient_id && message.sender_id === user?.id))) ||
        (chat.type === 'room' && chat.room?.id === message.room_id)
      ) {
        return {
          ...chat,
          last_message: message,
          unread_count: chat.id === activeChat?.id ? 0 : chat.unread_count + 1
        };
      }
      return chat;
    }));

    // Add notification if message is not from current user and not in active chat
    if (message.sender_id !== user?.id && 
        (!activeChat || 
         (activeChat.type === 'direct' && activeChat.user?.id !== message.sender_id) ||
         (activeChat.type === 'room' && activeChat.room?.id !== message.room_id))) {
      
      const notification: NotificationData = {
        id: `msg_${message.id}`,
        type: 'message',
        title: message.sender?.username || 'رسالة جديدة',
        message: message.content,
        timestamp: new Date(message.created_at),
        read: false
      };
      
      setNotifications(prev => [notification, ...prev]);
    }
  };

  const handleTyping = (typingData: TypingData) => {
    setTypingUsers(prev => {
      const filtered = prev.filter(t => t.user_id !== typingData.user_id);
      return [...filtered, typingData];
    });

    // Remove typing indicator after 3 seconds
    setTimeout(() => {
      setTypingUsers(prev => prev.filter(t => t.user_id !== typingData.user_id));
    }, 3000);
  };

  const handleUserStatus = (statusData: { user_id: number; is_online: boolean }) => {
    setChats(prev => prev.map(chat => {
      if (chat.type === 'direct' && chat.user?.id === statusData.user_id) {
        return {
          ...chat,
          is_online: statusData.is_online,
          user: chat.user ? { ...chat.user, is_online: statusData.is_online } : undefined
        };
      }
      return chat;
    }));

    // Add notification for user status change
    const notification: NotificationData = {
      id: `status_${statusData.user_id}_${Date.now()}`,
      type: statusData.is_online ? 'user_online' : 'user_offline',
      title: 'حالة المستخدم',
      message: statusData.is_online ? 'متصل الآن' : 'غير متصل',
      timestamp: new Date(),
      read: false
    };
    
    setNotifications(prev => [notification, ...prev.slice(0, 49)]); // Keep only last 50 notifications
  };

  const handleMessageRead = (data: { message_id: number; user_id: number }) => {
    setMessages(prev => prev.map(message => {
      if (message.id === data.message_id) {
        return { ...message, status: 'read' as any };
      }
      return message;
    }));
  };

  const handleNotification = (notificationData: any) => {
    const notification: NotificationData = {
      id: `notif_${Date.now()}`,
      type: notificationData.type || 'message',
      title: notificationData.title || 'إشعار جديد',
      message: notificationData.message || '',
      timestamp: new Date(),
      read: false
    };
    
    setNotifications(prev => [notification, ...prev.slice(0, 49)]);
  };

  const refreshChats = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      // Get direct message chats
      const users = await apiService.getUsers();
      const directChats: Chat[] = users.items
        .filter(u => u.id !== user.id)
        .map(u => ({
          id: `direct_${u.id}`,
          type: 'direct' as const,
          name: u.username,
          avatar: u.avatar_url,
          unread_count: 0,
          is_online: u.is_online,
          last_seen: u.last_seen,
          user: u
        }));

      // Get room chats
      const rooms = await apiService.getRooms();
      const roomChats: Chat[] = rooms.items.map(room => ({
        id: `room_${room.id}`,
        type: 'room' as const,
        name: room.name,
        unread_count: 0,
        room: room
      }));

      setChats([...directChats, ...roomChats]);
    } catch (error) {
      console.error('Error loading chats:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadMessages = async (chatId: string) => {
    setIsLoading(true);
    try {
      const [type, id] = chatId.split('_');
      const numericId = parseInt(id);

      const response = await apiService.getMessages(
        1, 
        100, 
        type === 'direct' ? numericId : undefined,
        type === 'room' ? numericId : undefined
      );

      setMessages(response.items.reverse());
    } catch (error) {
      console.error('Error loading messages:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const sendMessage = async (content: string) => {
    if (!activeChat || !user) return;

    try {
      const [type, id] = activeChat.id.split('_');
      const numericId = parseInt(id);

      if (websocketService.isConnected()) {
        websocketService.sendMessage(
          content,
          type === 'direct' ? numericId : undefined,
          type === 'room' ? numericId : undefined
        );
      } else {
        // Fallback to API if WebSocket is not connected
        await apiService.sendMessage({
          content,
          recipient_id: type === 'direct' ? numericId : undefined,
          room_id: type === 'room' ? numericId : undefined,
          message_type: type
        });
      }
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  };

  const markAsRead = (messageId: number) => {
    if (websocketService.isConnected()) {
      websocketService.markMessageAsRead(messageId);
    }
  };

  const sendTyping = () => {
    if (!activeChat || !websocketService.isConnected()) return;

    const [type, id] = activeChat.id.split('_');
    const numericId = parseInt(id);

    websocketService.sendTyping(
      type === 'direct' ? numericId : undefined,
      type === 'room' ? numericId : undefined
    );
  };

  const handleSetActiveChat = (chat: Chat | null) => {
    setActiveChat(chat);
    if (chat) {
      loadMessages(chat.id);
      
      // Mark chat as read
      setChats(prev => prev.map(c => 
        c.id === chat.id ? { ...c, unread_count: 0 } : c
      ));
    }
  };

  const clearNotifications = () => {
    setNotifications([]);
  };

  const markNotificationAsRead = (notificationId: string) => {
    setNotifications(prev => prev.map(notif => 
      notif.id === notificationId ? { ...notif, read: true } : notif
    ));
  };

  const value: ChatContextType = {
    chats,
    activeChat,
    messages,
    typingUsers,
    notifications,
    isLoading,
    isConnected,
    setActiveChat: handleSetActiveChat,
    sendMessage,
    loadMessages,
    markAsRead,
    sendTyping,
    refreshChats,
    clearNotifications,
    markNotificationAsRead,
  };

  return (
    <ChatContext.Provider value={value}>
      {children}
    </ChatContext.Provider>
  );
};

export const useChat = (): ChatContextType => {
  const context = useContext(ChatContext);
  if (context === undefined) {
    throw new Error('useChat must be used within a ChatProvider');
  }
  return context;
};
