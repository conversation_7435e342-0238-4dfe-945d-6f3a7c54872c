/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/home/<USER>/Desktop/time-chat/nextjs-chat/src/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fsrc%2Fcontext%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fsrc%2Fcontext%2FChatContext.tsx%22%2C%22ids%22%3A%5B%22ChatProvider%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fsrc%2Fcontext%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fsrc%2Fcontext%2FChatContext.tsx%22%2C%22ids%22%3A%5B%22ChatProvider%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/context/AuthContext.tsx */ \"(rsc)/./src/context/AuthContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/context/ChatContext.tsx */ \"(rsc)/./src/context/ChatContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZtYXplbiUyRkRlc2t0b3AlMkZ0aW1lLWNoYXQlMkZuZXh0anMtY2hhdCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZmb250JTJGZ29vZ2xlJTJGdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlMkZhcHAlMkZsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZob21lJTJGbWF6ZW4lMkZEZXNrdG9wJTJGdGltZS1jaGF0JTJGbmV4dGpzLWNoYXQlMkZzcmMlMkZhcHAlMkZnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZob21lJTJGbWF6ZW4lMkZEZXNrdG9wJTJGdGltZS1jaGF0JTJGbmV4dGpzLWNoYXQlMkZzcmMlMkZjb250ZXh0JTJGQXV0aENvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXV0aFByb3ZpZGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZtYXplbiUyRkRlc2t0b3AlMkZ0aW1lLWNoYXQlMkZuZXh0anMtY2hhdCUyRnNyYyUyRmNvbnRleHQlMkZDaGF0Q29udGV4dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJDaGF0UHJvdmlkZXIlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHNLQUE0STtBQUM1STtBQUNBLHNLQUE0SSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQXV0aFByb3ZpZGVyXCJdICovIFwiL2hvbWUvbWF6ZW4vRGVza3RvcC90aW1lLWNoYXQvbmV4dGpzLWNoYXQvc3JjL2NvbnRleHQvQXV0aENvbnRleHQudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJDaGF0UHJvdmlkZXJcIl0gKi8gXCIvaG9tZS9tYXplbi9EZXNrdG9wL3RpbWUtY2hhdC9uZXh0anMtY2hhdC9zcmMvY29udGV4dC9DaGF0Q29udGV4dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fsrc%2Fcontext%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fsrc%2Fcontext%2FChatContext.tsx%22%2C%22ids%22%3A%5B%22ChatProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZtYXplbiUyRkRlc2t0b3AlMkZ0aW1lLWNoYXQlMkZuZXh0anMtY2hhdCUyRnNyYyUyRmFwcCUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBK0YiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9ob21lL21hemVuL0Rlc2t0b3AvdGltZS1jaGF0L25leHRqcy1jaGF0L3NyYy9hcHAvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"ca1761f94adc\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvaG9tZS9tYXplbi9EZXNrdG9wL3RpbWUtY2hhdC9uZXh0anMtY2hhdC9zcmMvYXBwL2dsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiY2ExNzYxZjk0YWRjXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/AuthContext */ \"(rsc)/./src/context/AuthContext.tsx\");\n/* harmony import */ var _context_ChatContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/context/ChatContext */ \"(rsc)/./src/context/ChatContext.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: 'تايم شات - Time Chat',\n    description: 'تطبيق دردشة فوري رائع مع مميزات متقدمة',\n    keywords: [\n        'دردشة',\n        'chat',\n        'messaging',\n        'real-time'\n    ],\n    authors: [\n        {\n            name: 'Time Chat Team'\n        }\n    ],\n    viewport: 'width=device-width, initial-scale=1'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_ChatContext__WEBPACK_IMPORTED_MODULE_3__.ChatProvider, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/app/layout.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/app/layout.tsx\",\n                lineNumber: 25,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/app/layout.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/app/layout.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/app/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/home/<USER>/Desktop/time-chat/nextjs-chat/src/app/page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/context/AuthContext.tsx":
/*!*************************************!*\
  !*** ./src/context/AuthContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/home/<USER>/Desktop/time-chat/nextjs-chat/src/context/AuthContext.tsx",
"AuthProvider",
);const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/home/<USER>/Desktop/time-chat/nextjs-chat/src/context/AuthContext.tsx",
"useAuth",
);

/***/ }),

/***/ "(rsc)/./src/context/ChatContext.tsx":
/*!*************************************!*\
  !*** ./src/context/ChatContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ChatProvider: () => (/* binding */ ChatProvider),
/* harmony export */   useChat: () => (/* binding */ useChat)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ChatProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ChatProvider() from the server but ChatProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/home/<USER>/Desktop/time-chat/nextjs-chat/src/context/ChatContext.tsx",
"ChatProvider",
);const useChat = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useChat() from the server but useChat is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/home/<USER>/Desktop/time-chat/nextjs-chat/src/context/ChatContext.tsx",
"useChat",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fsrc%2Fcontext%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fsrc%2Fcontext%2FChatContext.tsx%22%2C%22ids%22%3A%5B%22ChatProvider%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fsrc%2Fcontext%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fsrc%2Fcontext%2FChatContext.tsx%22%2C%22ids%22%3A%5B%22ChatProvider%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/context/AuthContext.tsx */ \"(ssr)/./src/context/AuthContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/context/ChatContext.tsx */ \"(ssr)/./src/context/ChatContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZtYXplbiUyRkRlc2t0b3AlMkZ0aW1lLWNoYXQlMkZuZXh0anMtY2hhdCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZmb250JTJGZ29vZ2xlJTJGdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlMkZhcHAlMkZsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZob21lJTJGbWF6ZW4lMkZEZXNrdG9wJTJGdGltZS1jaGF0JTJGbmV4dGpzLWNoYXQlMkZzcmMlMkZhcHAlMkZnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZob21lJTJGbWF6ZW4lMkZEZXNrdG9wJTJGdGltZS1jaGF0JTJGbmV4dGpzLWNoYXQlMkZzcmMlMkZjb250ZXh0JTJGQXV0aENvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXV0aFByb3ZpZGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZtYXplbiUyRkRlc2t0b3AlMkZ0aW1lLWNoYXQlMkZuZXh0anMtY2hhdCUyRnNyYyUyRmNvbnRleHQlMkZDaGF0Q29udGV4dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJDaGF0UHJvdmlkZXIlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHNLQUE0STtBQUM1STtBQUNBLHNLQUE0SSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQXV0aFByb3ZpZGVyXCJdICovIFwiL2hvbWUvbWF6ZW4vRGVza3RvcC90aW1lLWNoYXQvbmV4dGpzLWNoYXQvc3JjL2NvbnRleHQvQXV0aENvbnRleHQudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJDaGF0UHJvdmlkZXJcIl0gKi8gXCIvaG9tZS9tYXplbi9EZXNrdG9wL3RpbWUtY2hhdC9uZXh0anMtY2hhdC9zcmMvY29udGV4dC9DaGF0Q29udGV4dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fsrc%2Fcontext%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fsrc%2Fcontext%2FChatContext.tsx%22%2C%22ids%22%3A%5B%22ChatProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZtYXplbiUyRkRlc2t0b3AlMkZ0aW1lLWNoYXQlMkZuZXh0anMtY2hhdCUyRnNyYyUyRmFwcCUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBK0YiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9ob21lL21hemVuL0Rlc2t0b3AvdGltZS1jaGF0L25leHRqcy1jaGF0L3NyYy9hcHAvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/context/AuthContext */ \"(ssr)/./src/context/AuthContext.tsx\");\n/* harmony import */ var _components_chat_ChatLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/chat/ChatLayout */ \"(ssr)/./src/components/chat/ChatLayout.tsx\");\n/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/LoadingSpinner */ \"(ssr)/./src/components/ui/LoadingSpinner.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction Home() {\n    const { isAuthenticated, isLoading } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (!isLoading && !isAuthenticated) {\n                router.push('/auth/login');\n            }\n        }\n    }[\"Home.useEffect\"], [\n        isAuthenticated,\n        isLoading,\n        router\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-whatsapp-teal to-whatsapp-green-dark\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                size: \"large\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/app/page.tsx\",\n                lineNumber: 22,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/app/page.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this);\n    }\n    if (!isAuthenticated) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_ChatLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/app/page.tsx\",\n        lineNumber: 31,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/chat/ChatArea.tsx":
/*!******************************************!*\
  !*** ./src/components/chat/ChatArea.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _context_ChatContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/ChatContext */ \"(ssr)/./src/context/ChatContext.tsx\");\n/* harmony import */ var _ChatHeader__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ChatHeader */ \"(ssr)/./src/components/chat/ChatHeader.tsx\");\n/* harmony import */ var _MessageList__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./MessageList */ \"(ssr)/./src/components/chat/MessageList.tsx\");\n/* harmony import */ var _MessageInput__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./MessageInput */ \"(ssr)/./src/components/chat/MessageInput.tsx\");\n/* harmony import */ var _TypingIndicator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./TypingIndicator */ \"(ssr)/./src/components/chat/TypingIndicator.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst ChatArea = ()=>{\n    const { activeChat, messages, typingUsers, isLoading } = (0,_context_ChatContext__WEBPACK_IMPORTED_MODULE_2__.useChat)();\n    if (!activeChat) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatHeader__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                chat: activeChat\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatArea.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 overflow-y-auto chat-bg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageList__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            messages: messages,\n                            isLoading: isLoading\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatArea.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TypingIndicator__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            typingUsers: typingUsers,\n                            activeChat: activeChat\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatArea.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatArea.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatArea.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageInput__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatArea.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatArea.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatArea);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/chat/ChatArea.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/chat/ChatHeader.tsx":
/*!********************************************!*\
  !*** ./src/components/chat/ChatHeader.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Circle_MoreVertical_Phone_Search_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,MoreVertical,Phone,Search,Users,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_MoreVertical_Phone_Search_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,MoreVertical,Phone,Search,Users,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_MoreVertical_Phone_Search_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,MoreVertical,Phone,Search,Users,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_MoreVertical_Phone_Search_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,MoreVertical,Phone,Search,Users,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_MoreVertical_Phone_Search_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,MoreVertical,Phone,Search,Users,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/video.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_MoreVertical_Phone_Search_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,MoreVertical,Phone,Search,Users,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/ellipsis-vertical.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst ChatHeader = ({ chat })=>{\n    const [showMenu, setShowMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const getStatusText = ()=>{\n        if (chat.type === 'room') {\n            return `${chat.room?.member_count || 0} عضو`;\n        }\n        if (chat.is_online) {\n            return 'متصل';\n        }\n        if (chat.last_seen) {\n            return `آخر ظهور ${(0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatTime)(chat.last_seen)}`;\n        }\n        return 'غير متصل';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white border-b border-gray-200 px-6 py-4 shadow-sm\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 rounded-full flex items-center justify-center text-white font-semibold overflow-hidden\",\n                                    style: {\n                                        backgroundColor: chat.avatar || (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.generateAvatar)(chat.name)\n                                    },\n                                    children: chat.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: chat.avatar,\n                                        alt: chat.name,\n                                        className: \"w-full h-full object-cover\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatHeader.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 17\n                                    }, undefined) : chat.type === 'room' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_MoreVertical_Phone_Search_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatHeader.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: chat.name.charAt(0).toUpperCase()\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatHeader.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatHeader.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 13\n                                }, undefined),\n                                chat.type === 'direct' && chat.is_online && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute bottom-0 left-0 w-3 h-3 bg-whatsapp-green rounded-full border-2 border-white\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatHeader.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatHeader.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"font-semibold text-gray-900 text-lg\",\n                                    children: chat.name\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatHeader.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1\",\n                                    children: [\n                                        chat.type === 'direct' && chat.is_online && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_MoreVertical_Phone_Search_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-2 h-2 text-whatsapp-green fill-current\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatHeader.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: getStatusText()\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatHeader.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatHeader.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatHeader.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatHeader.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"p-2 text-gray-600 hover:text-whatsapp-green hover:bg-gray-100 rounded-full transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_MoreVertical_Phone_Search_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatHeader.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatHeader.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, undefined),\n                        chat.type === 'direct' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"p-2 text-gray-600 hover:text-whatsapp-green hover:bg-gray-100 rounded-full transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_MoreVertical_Phone_Search_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatHeader.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatHeader.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"p-2 text-gray-600 hover:text-whatsapp-green hover:bg-gray-100 rounded-full transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_MoreVertical_Phone_Search_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatHeader.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatHeader.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowMenu(!showMenu),\n                                    className: \"p-2 text-gray-600 hover:text-whatsapp-green hover:bg-gray-100 rounded-full transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_MoreVertical_Phone_Search_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatHeader.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatHeader.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, undefined),\n                                showMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute left-0 top-12 bg-white rounded-lg shadow-lg py-2 w-48 z-10 border\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"w-full text-right px-4 py-2 text-gray-700 hover:bg-gray-100 transition-colors\",\n                                            children: \"عرض الملف الشخصي\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatHeader.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"w-full text-right px-4 py-2 text-gray-700 hover:bg-gray-100 transition-colors\",\n                                            children: \"البحث في المحادثة\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatHeader.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"w-full text-right px-4 py-2 text-gray-700 hover:bg-gray-100 transition-colors\",\n                                            children: \"كتم الإشعارات\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatHeader.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        chat.type === 'room' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                    className: \"my-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatHeader.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"w-full text-right px-4 py-2 text-gray-700 hover:bg-gray-100 transition-colors\",\n                                                    children: \"إعدادات المجموعة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatHeader.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"w-full text-right px-4 py-2 text-red-600 hover:bg-gray-100 transition-colors\",\n                                                    children: \"مغادرة المجموعة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatHeader.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatHeader.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatHeader.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatHeader.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatHeader.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatHeader.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatHeader);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9jaGF0L0NoYXRIZWFkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFd0M7QUFDeUM7QUFFeEI7QUFNekQsTUFBTVUsYUFBd0MsQ0FBQyxFQUFFQyxJQUFJLEVBQUU7SUFDckQsTUFBTSxDQUFDQyxVQUFVQyxZQUFZLEdBQUdaLCtDQUFRQSxDQUFDO0lBRXpDLE1BQU1hLGdCQUFnQjtRQUNwQixJQUFJSCxLQUFLSSxJQUFJLEtBQUssUUFBUTtZQUN4QixPQUFPLEdBQUdKLEtBQUtLLElBQUksRUFBRUMsZ0JBQWdCLEVBQUUsSUFBSSxDQUFDO1FBQzlDO1FBRUEsSUFBSU4sS0FBS08sU0FBUyxFQUFFO1lBQ2xCLE9BQU87UUFDVDtRQUVBLElBQUlQLEtBQUtRLFNBQVMsRUFBRTtZQUNsQixPQUFPLENBQUMsU0FBUyxFQUFFVixzREFBVUEsQ0FBQ0UsS0FBS1EsU0FBUyxHQUFHO1FBQ2pEO1FBRUEsT0FBTztJQUNUO0lBRUEscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7a0JBQ2IsNEVBQUNEO1lBQUlDLFdBQVU7OzhCQUViLDhEQUFDRDtvQkFBSUMsV0FBVTs7c0NBRWIsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQ0NDLFdBQVU7b0NBQ1ZDLE9BQU87d0NBQUVDLGlCQUFpQlosS0FBS2EsTUFBTSxJQUFJaEIsMERBQWNBLENBQUNHLEtBQUtjLElBQUk7b0NBQUU7OENBRWxFZCxLQUFLYSxNQUFNLGlCQUNWLDhEQUFDRTt3Q0FDQ0MsS0FBS2hCLEtBQUthLE1BQU07d0NBQ2hCSSxLQUFLakIsS0FBS2MsSUFBSTt3Q0FDZEosV0FBVTs7Ozs7b0RBRVZWLEtBQUtJLElBQUksS0FBSyx1QkFDaEIsOERBQUNWLHdIQUFLQTt3Q0FBQ2dCLFdBQVU7Ozs7O2tFQUVqQiw4REFBQ1E7a0RBQU1sQixLQUFLYyxJQUFJLENBQUNLLE1BQU0sQ0FBQyxHQUFHQyxXQUFXOzs7Ozs7Ozs7OztnQ0FLekNwQixLQUFLSSxJQUFJLEtBQUssWUFBWUosS0FBS08sU0FBUyxrQkFDdkMsOERBQUNFO29DQUFJQyxXQUFVOzs7Ozs7Ozs7Ozs7c0NBS25CLDhEQUFDRDs7OENBQ0MsOERBQUNZO29DQUFHWCxXQUFVOzhDQUF1Q1YsS0FBS2MsSUFBSTs7Ozs7OzhDQUM5RCw4REFBQ0w7b0NBQUlDLFdBQVU7O3dDQUNaVixLQUFLSSxJQUFJLEtBQUssWUFBWUosS0FBS08sU0FBUyxrQkFDdkMsOERBQUNYLHdIQUFNQTs0Q0FBQ2MsV0FBVTs7Ozs7O3NEQUVwQiw4REFBQ1k7NENBQUVaLFdBQVU7c0RBQXlCUDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQU01Qyw4REFBQ007b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDYTs0QkFBT2IsV0FBVTtzQ0FDaEIsNEVBQUNmLHdIQUFNQTtnQ0FBQ2UsV0FBVTs7Ozs7Ozs7Ozs7d0JBR25CVixLQUFLSSxJQUFJLEtBQUssMEJBQ2I7OzhDQUNFLDhEQUFDbUI7b0NBQU9iLFdBQVU7OENBQ2hCLDRFQUFDbkIsd0hBQUtBO3dDQUFDbUIsV0FBVTs7Ozs7Ozs7Ozs7OENBRW5CLDhEQUFDYTtvQ0FBT2IsV0FBVTs4Q0FDaEIsNEVBQUNsQix3SEFBS0E7d0NBQUNrQixXQUFVOzs7Ozs7Ozs7Ozs7O3NDQU12Qiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDYTtvQ0FDQ0MsU0FBUyxJQUFNdEIsWUFBWSxDQUFDRDtvQ0FDNUJTLFdBQVU7OENBRVYsNEVBQUNqQix3SEFBWUE7d0NBQUNpQixXQUFVOzs7Ozs7Ozs7OztnQ0FHekJULDBCQUNDLDhEQUFDUTtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNhOzRDQUFPYixXQUFVO3NEQUFnRjs7Ozs7O3NEQUdsRyw4REFBQ2E7NENBQU9iLFdBQVU7c0RBQWdGOzs7Ozs7c0RBR2xHLDhEQUFDYTs0Q0FBT2IsV0FBVTtzREFBZ0Y7Ozs7Ozt3Q0FHakdWLEtBQUtJLElBQUksS0FBSyx3QkFDYjs7OERBQ0UsOERBQUNxQjtvREFBR2YsV0FBVTs7Ozs7OzhEQUNkLDhEQUFDYTtvREFBT2IsV0FBVTs4REFBZ0Y7Ozs7Ozs4REFHbEcsOERBQUNhO29EQUFPYixXQUFVOzhEQUErRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVlySDtBQUVBLGlFQUFlWCxVQUFVQSxFQUFDIiwic291cmNlcyI6WyIvaG9tZS9tYXplbi9EZXNrdG9wL3RpbWUtY2hhdC9uZXh0anMtY2hhdC9zcmMvY29tcG9uZW50cy9jaGF0L0NoYXRIZWFkZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgUGhvbmUsIFZpZGVvLCBNb3JlVmVydGljYWwsIFVzZXJzLCBTZWFyY2gsIENpcmNsZSB9IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XG5pbXBvcnQgeyBDaGF0IH0gZnJvbSAnQC90eXBlcyc7XG5pbXBvcnQgeyBnZW5lcmF0ZUF2YXRhciwgZm9ybWF0VGltZSB9IGZyb20gJ0AvbGliL3V0aWxzJztcblxuaW50ZXJmYWNlIENoYXRIZWFkZXJQcm9wcyB7XG4gIGNoYXQ6IENoYXQ7XG59XG5cbmNvbnN0IENoYXRIZWFkZXI6IFJlYWN0LkZDPENoYXRIZWFkZXJQcm9wcz4gPSAoeyBjaGF0IH0pID0+IHtcbiAgY29uc3QgW3Nob3dNZW51LCBzZXRTaG93TWVudV0gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgY29uc3QgZ2V0U3RhdHVzVGV4dCA9ICgpID0+IHtcbiAgICBpZiAoY2hhdC50eXBlID09PSAncm9vbScpIHtcbiAgICAgIHJldHVybiBgJHtjaGF0LnJvb20/Lm1lbWJlcl9jb3VudCB8fCAwfSDYudi22YhgO1xuICAgIH1cbiAgICBcbiAgICBpZiAoY2hhdC5pc19vbmxpbmUpIHtcbiAgICAgIHJldHVybiAn2YXYqti12YQnO1xuICAgIH1cbiAgICBcbiAgICBpZiAoY2hhdC5sYXN0X3NlZW4pIHtcbiAgICAgIHJldHVybiBg2KLYrtixINi42YfZiNixICR7Zm9ybWF0VGltZShjaGF0Lmxhc3Rfc2Vlbil9YDtcbiAgICB9XG4gICAgXG4gICAgcmV0dXJuICfYutmK2LEg2YXYqti12YQnO1xuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBib3JkZXItYiBib3JkZXItZ3JheS0yMDAgcHgtNiBweS00IHNoYWRvdy1zbVwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgey8qIENoYXQgSW5mbyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICB7LyogQXZhdGFyICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgIDxkaXYgXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctMTAgaC0xMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgdGV4dC13aGl0ZSBmb250LXNlbWlib2xkIG92ZXJmbG93LWhpZGRlblwiXG4gICAgICAgICAgICAgIHN0eWxlPXt7IGJhY2tncm91bmRDb2xvcjogY2hhdC5hdmF0YXIgfHwgZ2VuZXJhdGVBdmF0YXIoY2hhdC5uYW1lKSB9fVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7Y2hhdC5hdmF0YXIgPyAoXG4gICAgICAgICAgICAgICAgPGltZyBcbiAgICAgICAgICAgICAgICAgIHNyYz17Y2hhdC5hdmF0YXJ9IFxuICAgICAgICAgICAgICAgICAgYWx0PXtjaGF0Lm5hbWV9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgaC1mdWxsIG9iamVjdC1jb3ZlclwiXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgKSA6IGNoYXQudHlwZSA9PT0gJ3Jvb20nID8gKFxuICAgICAgICAgICAgICAgIDxVc2VycyBjbGFzc05hbWU9XCJ3LTUgaC01XCIgLz5cbiAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICA8c3Bhbj57Y2hhdC5uYW1lLmNoYXJBdCgwKS50b1VwcGVyQ2FzZSgpfTwvc3Bhbj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgXG4gICAgICAgICAgICB7LyogT25saW5lIGluZGljYXRvciBmb3IgZGlyZWN0IGNoYXRzICovfVxuICAgICAgICAgICAge2NoYXQudHlwZSA9PT0gJ2RpcmVjdCcgJiYgY2hhdC5pc19vbmxpbmUgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGJvdHRvbS0wIGxlZnQtMCB3LTMgaC0zIGJnLXdoYXRzYXBwLWdyZWVuIHJvdW5kZWQtZnVsbCBib3JkZXItMiBib3JkZXItd2hpdGVcIj48L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogTmFtZSBhbmQgU3RhdHVzICovfVxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIHRleHQtbGdcIj57Y2hhdC5uYW1lfTwvaDI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMVwiPlxuICAgICAgICAgICAgICB7Y2hhdC50eXBlID09PSAnZGlyZWN0JyAmJiBjaGF0LmlzX29ubGluZSAmJiAoXG4gICAgICAgICAgICAgICAgPENpcmNsZSBjbGFzc05hbWU9XCJ3LTIgaC0yIHRleHQtd2hhdHNhcHAtZ3JlZW4gZmlsbC1jdXJyZW50XCIgLz5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+e2dldFN0YXR1c1RleHQoKX08L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIEFjdGlvbiBCdXR0b25zICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwicC0yIHRleHQtZ3JheS02MDAgaG92ZXI6dGV4dC13aGF0c2FwcC1ncmVlbiBob3ZlcjpiZy1ncmF5LTEwMCByb3VuZGVkLWZ1bGwgdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgICAgIDxTZWFyY2ggY2xhc3NOYW1lPVwidy01IGgtNVwiIC8+XG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgXG4gICAgICAgICAge2NoYXQudHlwZSA9PT0gJ2RpcmVjdCcgJiYgKFxuICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJwLTIgdGV4dC1ncmF5LTYwMCBob3Zlcjp0ZXh0LXdoYXRzYXBwLWdyZWVuIGhvdmVyOmJnLWdyYXktMTAwIHJvdW5kZWQtZnVsbCB0cmFuc2l0aW9uLWNvbG9yc1wiPlxuICAgICAgICAgICAgICAgIDxQaG9uZSBjbGFzc05hbWU9XCJ3LTUgaC01XCIgLz5cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwicC0yIHRleHQtZ3JheS02MDAgaG92ZXI6dGV4dC13aGF0c2FwcC1ncmVlbiBob3ZlcjpiZy1ncmF5LTEwMCByb3VuZGVkLWZ1bGwgdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgICAgICAgICA8VmlkZW8gY2xhc3NOYW1lPVwidy01IGgtNVwiIC8+XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC8+XG4gICAgICAgICAgKX1cblxuICAgICAgICAgIHsvKiBNZW51ICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd01lbnUoIXNob3dNZW51KX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0yIHRleHQtZ3JheS02MDAgaG92ZXI6dGV4dC13aGF0c2FwcC1ncmVlbiBob3ZlcjpiZy1ncmF5LTEwMCByb3VuZGVkLWZ1bGwgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8TW9yZVZlcnRpY2FsIGNsYXNzTmFtZT1cInctNSBoLTVcIiAvPlxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIHtzaG93TWVudSAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgbGVmdC0wIHRvcC0xMiBiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1sZyBweS0yIHctNDggei0xMCBib3JkZXJcIj5cbiAgICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cInctZnVsbCB0ZXh0LXJpZ2h0IHB4LTQgcHktMiB0ZXh0LWdyYXktNzAwIGhvdmVyOmJnLWdyYXktMTAwIHRyYW5zaXRpb24tY29sb3JzXCI+XG4gICAgICAgICAgICAgICAgICDYudix2LYg2KfZhNmF2YTZgSDYp9mE2LTYrti12YpcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cInctZnVsbCB0ZXh0LXJpZ2h0IHB4LTQgcHktMiB0ZXh0LWdyYXktNzAwIGhvdmVyOmJnLWdyYXktMTAwIHRyYW5zaXRpb24tY29sb3JzXCI+XG4gICAgICAgICAgICAgICAgICDYp9mE2KjYrdirINmB2Yog2KfZhNmF2K3Yp9iv2KvYqVxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwidy1mdWxsIHRleHQtcmlnaHQgcHgtNCBweS0yIHRleHQtZ3JheS03MDAgaG92ZXI6YmctZ3JheS0xMDAgdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgICAgICAgICAgINmD2KrZhSDYp9mE2KXYtNi52KfYsdin2KpcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICB7Y2hhdC50eXBlID09PSAncm9vbScgJiYgKFxuICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgPGhyIGNsYXNzTmFtZT1cIm15LTFcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cInctZnVsbCB0ZXh0LXJpZ2h0IHB4LTQgcHktMiB0ZXh0LWdyYXktNzAwIGhvdmVyOmJnLWdyYXktMTAwIHRyYW5zaXRpb24tY29sb3JzXCI+XG4gICAgICAgICAgICAgICAgICAgICAg2KXYudiv2KfYr9in2Kog2KfZhNmF2KzZhdmI2LnYqVxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJ3LWZ1bGwgdGV4dC1yaWdodCBweC00IHB5LTIgdGV4dC1yZWQtNjAwIGhvdmVyOmJnLWdyYXktMTAwIHRyYW5zaXRpb24tY29sb3JzXCI+XG4gICAgICAgICAgICAgICAgICAgICAg2YXYutin2K/YsdipINin2YTZhdis2YXZiNi52KlcbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBDaGF0SGVhZGVyO1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJQaG9uZSIsIlZpZGVvIiwiTW9yZVZlcnRpY2FsIiwiVXNlcnMiLCJTZWFyY2giLCJDaXJjbGUiLCJnZW5lcmF0ZUF2YXRhciIsImZvcm1hdFRpbWUiLCJDaGF0SGVhZGVyIiwiY2hhdCIsInNob3dNZW51Iiwic2V0U2hvd01lbnUiLCJnZXRTdGF0dXNUZXh0IiwidHlwZSIsInJvb20iLCJtZW1iZXJfY291bnQiLCJpc19vbmxpbmUiLCJsYXN0X3NlZW4iLCJkaXYiLCJjbGFzc05hbWUiLCJzdHlsZSIsImJhY2tncm91bmRDb2xvciIsImF2YXRhciIsIm5hbWUiLCJpbWciLCJzcmMiLCJhbHQiLCJzcGFuIiwiY2hhckF0IiwidG9VcHBlckNhc2UiLCJoMiIsInAiLCJidXR0b24iLCJvbkNsaWNrIiwiaHIiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/chat/ChatHeader.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/chat/ChatLayout.tsx":
/*!********************************************!*\
  !*** ./src/components/chat/ChatLayout.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _context_ChatContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/ChatContext */ \"(ssr)/./src/context/ChatContext.tsx\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Sidebar */ \"(ssr)/./src/components/chat/Sidebar.tsx\");\n/* harmony import */ var _ChatArea__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ChatArea */ \"(ssr)/./src/components/chat/ChatArea.tsx\");\n/* harmony import */ var _WelcomeScreen__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./WelcomeScreen */ \"(ssr)/./src/components/chat/WelcomeScreen.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst ChatLayout = ()=>{\n    const { activeChat } = (0,_context_ChatContext__WEBPACK_IMPORTED_MODULE_2__.useChat)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-gray-100 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-1/3 min-w-[320px] max-w-[400px] bg-white border-r border-gray-200 flex flex-col\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatLayout.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatLayout.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: activeChat ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatArea__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatLayout.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_WelcomeScreen__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatLayout.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatLayout.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatLayout.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatLayout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9jaGF0L0NoYXRMYXlvdXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFFMEI7QUFDc0I7QUFDaEI7QUFDRTtBQUNVO0FBRTVDLE1BQU1LLGFBQXVCO0lBQzNCLE1BQU0sRUFBRUMsVUFBVSxFQUFFLEdBQUdMLDZEQUFPQTtJQUU5QixxQkFDRSw4REFBQ007UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNEO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDTixnREFBT0E7Ozs7Ozs7Ozs7MEJBSVYsOERBQUNLO2dCQUFJQyxXQUFVOzBCQUNaRiwyQkFDQyw4REFBQ0gsaURBQVFBOzs7OzhDQUVULDhEQUFDQyxzREFBYUE7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLeEI7QUFFQSxpRUFBZUMsVUFBVUEsRUFBQyIsInNvdXJjZXMiOlsiL2hvbWUvbWF6ZW4vRGVza3RvcC90aW1lLWNoYXQvbmV4dGpzLWNoYXQvc3JjL2NvbXBvbmVudHMvY2hhdC9DaGF0TGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VDaGF0IH0gZnJvbSAnQC9jb250ZXh0L0NoYXRDb250ZXh0JztcbmltcG9ydCBTaWRlYmFyIGZyb20gJy4vU2lkZWJhcic7XG5pbXBvcnQgQ2hhdEFyZWEgZnJvbSAnLi9DaGF0QXJlYSc7XG5pbXBvcnQgV2VsY29tZVNjcmVlbiBmcm9tICcuL1dlbGNvbWVTY3JlZW4nO1xuXG5jb25zdCBDaGF0TGF5b3V0OiBSZWFjdC5GQyA9ICgpID0+IHtcbiAgY29uc3QgeyBhY3RpdmVDaGF0IH0gPSB1c2VDaGF0KCk7XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaC1zY3JlZW4gYmctZ3JheS0xMDAgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICB7LyogU2lkZWJhciAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xLzMgbWluLXctWzMyMHB4XSBtYXgtdy1bNDAwcHhdIGJnLXdoaXRlIGJvcmRlci1yIGJvcmRlci1ncmF5LTIwMCBmbGV4IGZsZXgtY29sXCI+XG4gICAgICAgIDxTaWRlYmFyIC8+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIE1haW4gQ2hhdCBBcmVhICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgZmxleCBmbGV4LWNvbFwiPlxuICAgICAgICB7YWN0aXZlQ2hhdCA/IChcbiAgICAgICAgICA8Q2hhdEFyZWEgLz5cbiAgICAgICAgKSA6IChcbiAgICAgICAgICA8V2VsY29tZVNjcmVlbiAvPlxuICAgICAgICApfVxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBDaGF0TGF5b3V0O1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlQ2hhdCIsIlNpZGViYXIiLCJDaGF0QXJlYSIsIldlbGNvbWVTY3JlZW4iLCJDaGF0TGF5b3V0IiwiYWN0aXZlQ2hhdCIsImRpdiIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/chat/ChatLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/chat/ChatList.tsx":
/*!******************************************!*\
  !*** ./src/components/chat/ChatList.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Check_CheckCheck_Circle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,CheckCheck,Circle,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_CheckCheck_Circle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,CheckCheck,Circle,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_CheckCheck_Circle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Check,CheckCheck,Circle,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Check_CheckCheck_Circle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Check,CheckCheck,Circle,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle.js\");\n/* harmony import */ var _context_ChatContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/ChatContext */ \"(ssr)/./src/context/ChatContext.tsx\");\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/context/AuthContext */ \"(ssr)/./src/context/AuthContext.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst ChatList = ({ chats })=>{\n    const { activeChat, setActiveChat } = (0,_context_ChatContext__WEBPACK_IMPORTED_MODULE_2__.useChat)();\n    const { user } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const getMessageStatus = (message)=>{\n        if (!message || message.sender_id !== user?.id) return null;\n        switch(message.status){\n            case 'sent':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CheckCheck_Circle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-4 h-4 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatList.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 16\n                }, undefined);\n            case 'delivered':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CheckCheck_Circle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-4 h-4 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatList.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 16\n                }, undefined);\n            case 'read':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CheckCheck_Circle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-4 h-4 text-whatsapp-blue\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatList.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return null;\n        }\n    };\n    if (chats.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center h-full text-gray-500 p-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CheckCheck_Circle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-12 h-12 mb-4 opacity-50\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatList.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-center\",\n                    children: \"لا توجد محادثات بعد\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatList.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-center mt-2\",\n                    children: \"ابدأ محادثة جديدة مع أصدقائك\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatList.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatList.tsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"divide-y divide-gray-100\",\n        children: chats.map((chat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                onClick: ()=>setActiveChat(chat),\n                className: `p-4 cursor-pointer transition-all duration-200 hover:bg-gray-50 ${activeChat?.id === chat.id ? 'bg-whatsapp-green-light border-r-4 border-whatsapp-green' : ''}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative flex-shrink-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 rounded-full flex items-center justify-center text-white font-semibold text-lg overflow-hidden\",\n                                    style: {\n                                        backgroundColor: chat.avatar || (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.generateAvatar)(chat.name)\n                                    },\n                                    children: chat.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: chat.avatar,\n                                        alt: chat.name,\n                                        className: \"w-full h-full object-cover\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatList.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 19\n                                    }, undefined) : chat.type === 'room' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CheckCheck_Circle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatList.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 19\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: chat.name.charAt(0).toUpperCase()\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatList.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatList.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 15\n                                }, undefined),\n                                chat.type === 'direct' && chat.is_online && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute bottom-0 left-0 w-3 h-3 bg-whatsapp-green rounded-full border-2 border-white\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatList.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatList.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-900 truncate\",\n                                            children: chat.name\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatList.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-1\",\n                                            children: [\n                                                chat.last_message && getMessageStatus(chat.last_message),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: chat.last_message && (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatTime)(chat.last_message.created_at)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatList.tsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatList.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatList.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mt-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 truncate\",\n                                            children: chat.last_message ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    chat.last_message.sender_id === user?.id && 'أنت: ',\n                                                    truncateMessage(chat.last_message.content)\n                                                ]\n                                            }, void 0, true) : chat.type === 'direct' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    chat.is_online && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CheckCheck_Circle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-2 h-2 text-whatsapp-green fill-current mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatList.tsx\",\n                                                        lineNumber: 102,\n                                                        columnNumber: 42\n                                                    }, undefined),\n                                                    chat.is_online ? 'متصل' : `آخر ظهور ${chat.last_seen ? (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatTime)(chat.last_seen) : 'غير معروف'}`\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatList.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 21\n                                            }, undefined) : 'لا توجد رسائل'\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatList.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        chat.unread_count > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-whatsapp-green text-white text-xs rounded-full min-w-[20px] h-5 flex items-center justify-center px-1 animate-bounce-in\",\n                                            children: chat.unread_count > 99 ? '99+' : chat.unread_count\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatList.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatList.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatList.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatList.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 11\n                }, undefined)\n            }, chat.id, false, {\n                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatList.tsx\",\n                lineNumber: 46,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ChatList.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, undefined);\n};\n// Helper function to truncate message content\nconst truncateMessage = (content, maxLength = 50)=>{\n    if (content.length <= maxLength) return content;\n    return content.substring(0, maxLength) + '...';\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatList);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9jaGF0L0NoYXRMaXN0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBRTBCO0FBQ3NDO0FBRWhCO0FBQ0E7QUFDdUI7QUFNdkUsTUFBTVMsV0FBb0MsQ0FBQyxFQUFFQyxLQUFLLEVBQUU7SUFDbEQsTUFBTSxFQUFFQyxVQUFVLEVBQUVDLGFBQWEsRUFBRSxHQUFHUCw2REFBT0E7SUFDN0MsTUFBTSxFQUFFUSxJQUFJLEVBQUUsR0FBR1AsNkRBQU9BO0lBRXhCLE1BQU1RLG1CQUFtQixDQUFDQztRQUN4QixJQUFJLENBQUNBLFdBQVdBLFFBQVFDLFNBQVMsS0FBS0gsTUFBTUksSUFBSSxPQUFPO1FBRXZELE9BQVFGLFFBQVFHLE1BQU07WUFDcEIsS0FBSztnQkFDSCxxQkFBTyw4REFBQ2pCLHlHQUFLQTtvQkFBQ2tCLFdBQVU7Ozs7OztZQUMxQixLQUFLO2dCQUNILHFCQUFPLDhEQUFDakIseUdBQVVBO29CQUFDaUIsV0FBVTs7Ozs7O1lBQy9CLEtBQUs7Z0JBQ0gscUJBQU8sOERBQUNqQix5R0FBVUE7b0JBQUNpQixXQUFVOzs7Ozs7WUFDL0I7Z0JBQ0UsT0FBTztRQUNYO0lBQ0Y7SUFFQSxJQUFJVCxNQUFNVSxNQUFNLEtBQUssR0FBRztRQUN0QixxQkFDRSw4REFBQ0M7WUFBSUYsV0FBVTs7OEJBQ2IsOERBQUNoQix5R0FBS0E7b0JBQUNnQixXQUFVOzs7Ozs7OEJBQ2pCLDhEQUFDRztvQkFBRUgsV0FBVTs4QkFBYzs7Ozs7OzhCQUMzQiw4REFBQ0c7b0JBQUVILFdBQVU7OEJBQTJCOzs7Ozs7Ozs7Ozs7SUFHOUM7SUFFQSxxQkFDRSw4REFBQ0U7UUFBSUYsV0FBVTtrQkFDWlQsTUFBTWEsR0FBRyxDQUFDLENBQUNDLHFCQUNWLDhEQUFDSDtnQkFFQ0ksU0FBUyxJQUFNYixjQUFjWTtnQkFDN0JMLFdBQVcsQ0FBQyxnRUFBZ0UsRUFDMUVSLFlBQVlNLE9BQU9PLEtBQUtQLEVBQUUsR0FBRyw2REFBNkQsSUFDMUY7MEJBRUYsNEVBQUNJO29CQUFJRixXQUFVOztzQ0FFYiw4REFBQ0U7NEJBQUlGLFdBQVU7OzhDQUNiLDhEQUFDRTtvQ0FDQ0YsV0FBVTtvQ0FDVk8sT0FBTzt3Q0FBRUMsaUJBQWlCSCxLQUFLSSxNQUFNLElBQUlwQiwwREFBY0EsQ0FBQ2dCLEtBQUtLLElBQUk7b0NBQUU7OENBRWxFTCxLQUFLSSxNQUFNLGlCQUNWLDhEQUFDRTt3Q0FDQ0MsS0FBS1AsS0FBS0ksTUFBTTt3Q0FDaEJJLEtBQUtSLEtBQUtLLElBQUk7d0NBQ2RWLFdBQVU7Ozs7O29EQUVWSyxLQUFLUyxJQUFJLEtBQUssdUJBQ2hCLDhEQUFDOUIseUdBQUtBO3dDQUFDZ0IsV0FBVTs7Ozs7a0VBRWpCLDhEQUFDZTtrREFBTVYsS0FBS0ssSUFBSSxDQUFDTSxNQUFNLENBQUMsR0FBR0MsV0FBVzs7Ozs7Ozs7Ozs7Z0NBS3pDWixLQUFLUyxJQUFJLEtBQUssWUFBWVQsS0FBS2EsU0FBUyxrQkFDdkMsOERBQUNoQjtvQ0FBSUYsV0FBVTs7Ozs7Ozs7Ozs7O3NDQUtuQiw4REFBQ0U7NEJBQUlGLFdBQVU7OzhDQUNiLDhEQUFDRTtvQ0FBSUYsV0FBVTs7c0RBQ2IsOERBQUNtQjs0Q0FBR25CLFdBQVU7c0RBQ1hLLEtBQUtLLElBQUk7Ozs7OztzREFFWiw4REFBQ1I7NENBQUlGLFdBQVU7O2dEQUNaSyxLQUFLZSxZQUFZLElBQUl6QixpQkFBaUJVLEtBQUtlLFlBQVk7OERBQ3hELDhEQUFDTDtvREFBS2YsV0FBVTs4REFDYkssS0FBS2UsWUFBWSxJQUFJaEMsc0RBQVVBLENBQUNpQixLQUFLZSxZQUFZLENBQUNDLFVBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FLbkUsOERBQUNuQjtvQ0FBSUYsV0FBVTs7c0RBQ2IsOERBQUNHOzRDQUFFSCxXQUFVO3NEQUNWSyxLQUFLZSxZQUFZLGlCQUNoQjs7b0RBQ0dmLEtBQUtlLFlBQVksQ0FBQ3ZCLFNBQVMsS0FBS0gsTUFBTUksTUFBTTtvREFDNUN3QixnQkFBZ0JqQixLQUFLZSxZQUFZLENBQUNHLE9BQU87OytEQUUxQ2xCLEtBQUtTLElBQUksS0FBSyx5QkFDaEIsOERBQUNDO2dEQUFLZixXQUFVOztvREFDYkssS0FBS2EsU0FBUyxrQkFBSSw4REFBQ2pDLHlHQUFNQTt3REFBQ2UsV0FBVTs7Ozs7O29EQUNwQ0ssS0FBS2EsU0FBUyxHQUFHLFNBQVMsQ0FBQyxTQUFTLEVBQUViLEtBQUttQixTQUFTLEdBQUdwQyxzREFBVUEsQ0FBQ2lCLEtBQUttQixTQUFTLElBQUksYUFBYTs7Ozs7OzREQUdwRzs7Ozs7O3dDQUtIbkIsS0FBS29CLFlBQVksR0FBRyxtQkFDbkIsOERBQUN2Qjs0Q0FBSUYsV0FBVTtzREFDWkssS0FBS29CLFlBQVksR0FBRyxLQUFLLFFBQVFwQixLQUFLb0IsWUFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2VBbEV4RHBCLEtBQUtQLEVBQUU7Ozs7Ozs7Ozs7QUE0RXRCO0FBRUEsOENBQThDO0FBQzlDLE1BQU13QixrQkFBa0IsQ0FBQ0MsU0FBaUJHLFlBQW9CLEVBQUU7SUFDOUQsSUFBSUgsUUFBUXRCLE1BQU0sSUFBSXlCLFdBQVcsT0FBT0g7SUFDeEMsT0FBT0EsUUFBUUksU0FBUyxDQUFDLEdBQUdELGFBQWE7QUFDM0M7QUFFQSxpRUFBZXBDLFFBQVFBLEVBQUMiLCJzb3VyY2VzIjpbIi9ob21lL21hemVuL0Rlc2t0b3AvdGltZS1jaGF0L25leHRqcy1jaGF0L3NyYy9jb21wb25lbnRzL2NoYXQvQ2hhdExpc3QudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IENoZWNrLCBDaGVja0NoZWNrLCBVc2VycywgQ2lyY2xlIH0gZnJvbSAnbHVjaWRlLXJlYWN0JztcbmltcG9ydCB7IENoYXQgfSBmcm9tICdAL3R5cGVzJztcbmltcG9ydCB7IHVzZUNoYXQgfSBmcm9tICdAL2NvbnRleHQvQ2hhdENvbnRleHQnO1xuaW1wb3J0IHsgdXNlQXV0aCB9IGZyb20gJ0AvY29udGV4dC9BdXRoQ29udGV4dCc7XG5pbXBvcnQgeyBmb3JtYXRUaW1lLCB0cnVuY2F0ZVRleHQsIGdlbmVyYXRlQXZhdGFyIH0gZnJvbSAnQC9saWIvdXRpbHMnO1xuXG5pbnRlcmZhY2UgQ2hhdExpc3RQcm9wcyB7XG4gIGNoYXRzOiBDaGF0W107XG59XG5cbmNvbnN0IENoYXRMaXN0OiBSZWFjdC5GQzxDaGF0TGlzdFByb3BzPiA9ICh7IGNoYXRzIH0pID0+IHtcbiAgY29uc3QgeyBhY3RpdmVDaGF0LCBzZXRBY3RpdmVDaGF0IH0gPSB1c2VDaGF0KCk7XG4gIGNvbnN0IHsgdXNlciB9ID0gdXNlQXV0aCgpO1xuXG4gIGNvbnN0IGdldE1lc3NhZ2VTdGF0dXMgPSAobWVzc2FnZTogYW55KSA9PiB7XG4gICAgaWYgKCFtZXNzYWdlIHx8IG1lc3NhZ2Uuc2VuZGVyX2lkICE9PSB1c2VyPy5pZCkgcmV0dXJuIG51bGw7XG4gICAgXG4gICAgc3dpdGNoIChtZXNzYWdlLnN0YXR1cykge1xuICAgICAgY2FzZSAnc2VudCc6XG4gICAgICAgIHJldHVybiA8Q2hlY2sgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LWdyYXktNDAwXCIgLz47XG4gICAgICBjYXNlICdkZWxpdmVyZWQnOlxuICAgICAgICByZXR1cm4gPENoZWNrQ2hlY2sgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LWdyYXktNDAwXCIgLz47XG4gICAgICBjYXNlICdyZWFkJzpcbiAgICAgICAgcmV0dXJuIDxDaGVja0NoZWNrIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC13aGF0c2FwcC1ibHVlXCIgLz47XG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG4gIH07XG5cbiAgaWYgKGNoYXRzLmxlbmd0aCA9PT0gMCkge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGgtZnVsbCB0ZXh0LWdyYXktNTAwIHAtOFwiPlxuICAgICAgICA8VXNlcnMgY2xhc3NOYW1lPVwidy0xMiBoLTEyIG1iLTQgb3BhY2l0eS01MFwiIC8+XG4gICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+2YTYpyDYqtmI2KzYryDZhdit2KfYr9ir2KfYqiDYqNi52K88L3A+XG4gICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1jZW50ZXIgbXQtMlwiPtin2KjYr9ijINmF2K3Yp9iv2KvYqSDYrNiv2YrYr9ipINmF2Lkg2KPYtdiv2YLYp9im2YM8L3A+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImRpdmlkZS15IGRpdmlkZS1ncmF5LTEwMFwiPlxuICAgICAge2NoYXRzLm1hcCgoY2hhdCkgPT4gKFxuICAgICAgICA8ZGl2XG4gICAgICAgICAga2V5PXtjaGF0LmlkfVxuICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEFjdGl2ZUNoYXQoY2hhdCl9XG4gICAgICAgICAgY2xhc3NOYW1lPXtgcC00IGN1cnNvci1wb2ludGVyIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBob3ZlcjpiZy1ncmF5LTUwICR7XG4gICAgICAgICAgICBhY3RpdmVDaGF0Py5pZCA9PT0gY2hhdC5pZCA/ICdiZy13aGF0c2FwcC1ncmVlbi1saWdodCBib3JkZXItci00IGJvcmRlci13aGF0c2FwcC1ncmVlbicgOiAnJ1xuICAgICAgICAgIH1gfVxuICAgICAgICA+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgIHsvKiBBdmF0YXIgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIGZsZXgtc2hyaW5rLTBcIj5cbiAgICAgICAgICAgICAgPGRpdiBcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTEyIGgtMTIgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHRleHQtd2hpdGUgZm9udC1zZW1pYm9sZCB0ZXh0LWxnIG92ZXJmbG93LWhpZGRlblwiXG4gICAgICAgICAgICAgICAgc3R5bGU9e3sgYmFja2dyb3VuZENvbG9yOiBjaGF0LmF2YXRhciB8fCBnZW5lcmF0ZUF2YXRhcihjaGF0Lm5hbWUpIH19XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7Y2hhdC5hdmF0YXIgPyAoXG4gICAgICAgICAgICAgICAgICA8aW1nIFxuICAgICAgICAgICAgICAgICAgICBzcmM9e2NoYXQuYXZhdGFyfSBcbiAgICAgICAgICAgICAgICAgICAgYWx0PXtjaGF0Lm5hbWV9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBoLWZ1bGwgb2JqZWN0LWNvdmVyXCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgKSA6IGNoYXQudHlwZSA9PT0gJ3Jvb20nID8gKFxuICAgICAgICAgICAgICAgICAgPFVzZXJzIGNsYXNzTmFtZT1cInctNiBoLTZcIiAvPlxuICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICA8c3Bhbj57Y2hhdC5uYW1lLmNoYXJBdCgwKS50b1VwcGVyQ2FzZSgpfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIHsvKiBPbmxpbmUgaW5kaWNhdG9yIGZvciBkaXJlY3QgY2hhdHMgKi99XG4gICAgICAgICAgICAgIHtjaGF0LnR5cGUgPT09ICdkaXJlY3QnICYmIGNoYXQuaXNfb25saW5lICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGJvdHRvbS0wIGxlZnQtMCB3LTMgaC0zIGJnLXdoYXRzYXBwLWdyZWVuIHJvdW5kZWQtZnVsbCBib3JkZXItMiBib3JkZXItd2hpdGVcIj48L2Rpdj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogQ2hhdCBJbmZvICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgbWluLXctMFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgdHJ1bmNhdGVcIj5cbiAgICAgICAgICAgICAgICAgIHtjaGF0Lm5hbWV9XG4gICAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMVwiPlxuICAgICAgICAgICAgICAgICAge2NoYXQubGFzdF9tZXNzYWdlICYmIGdldE1lc3NhZ2VTdGF0dXMoY2hhdC5sYXN0X21lc3NhZ2UpfVxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgIHtjaGF0Lmxhc3RfbWVzc2FnZSAmJiBmb3JtYXRUaW1lKGNoYXQubGFzdF9tZXNzYWdlLmNyZWF0ZWRfYXQpfVxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG10LTFcIj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgdHJ1bmNhdGVcIj5cbiAgICAgICAgICAgICAgICAgIHtjaGF0Lmxhc3RfbWVzc2FnZSA/IChcbiAgICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgICB7Y2hhdC5sYXN0X21lc3NhZ2Uuc2VuZGVyX2lkID09PSB1c2VyPy5pZCAmJiAn2KPZhtiqOiAnfVxuICAgICAgICAgICAgICAgICAgICAgIHt0cnVuY2F0ZU1lc3NhZ2UoY2hhdC5sYXN0X21lc3NhZ2UuY29udGVudCl9XG4gICAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICAgKSA6IGNoYXQudHlwZSA9PT0gJ2RpcmVjdCcgPyAoXG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAge2NoYXQuaXNfb25saW5lICYmIDxDaXJjbGUgY2xhc3NOYW1lPVwidy0yIGgtMiB0ZXh0LXdoYXRzYXBwLWdyZWVuIGZpbGwtY3VycmVudCBtci0xXCIgLz59XG4gICAgICAgICAgICAgICAgICAgICAge2NoYXQuaXNfb25saW5lID8gJ9mF2KrYtdmEJyA6IGDYotiu2LEg2LjZh9mI2LEgJHtjaGF0Lmxhc3Rfc2VlbiA/IGZvcm1hdFRpbWUoY2hhdC5sYXN0X3NlZW4pIDogJ9i62YrYsSDZhdi52LHZiNmBJ31gfVxuICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAn2YTYpyDYqtmI2KzYryDYsdiz2KfYptmEJ1xuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgey8qIFVucmVhZCBjb3VudCAqL31cbiAgICAgICAgICAgICAgICB7Y2hhdC51bnJlYWRfY291bnQgPiAwICYmIChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hhdHNhcHAtZ3JlZW4gdGV4dC13aGl0ZSB0ZXh0LXhzIHJvdW5kZWQtZnVsbCBtaW4tdy1bMjBweF0gaC01IGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHB4LTEgYW5pbWF0ZS1ib3VuY2UtaW5cIj5cbiAgICAgICAgICAgICAgICAgICAge2NoYXQudW5yZWFkX2NvdW50ID4gOTkgPyAnOTkrJyA6IGNoYXQudW5yZWFkX2NvdW50fVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApKX1cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG5cbi8vIEhlbHBlciBmdW5jdGlvbiB0byB0cnVuY2F0ZSBtZXNzYWdlIGNvbnRlbnRcbmNvbnN0IHRydW5jYXRlTWVzc2FnZSA9IChjb250ZW50OiBzdHJpbmcsIG1heExlbmd0aDogbnVtYmVyID0gNTApID0+IHtcbiAgaWYgKGNvbnRlbnQubGVuZ3RoIDw9IG1heExlbmd0aCkgcmV0dXJuIGNvbnRlbnQ7XG4gIHJldHVybiBjb250ZW50LnN1YnN0cmluZygwLCBtYXhMZW5ndGgpICsgJy4uLic7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBDaGF0TGlzdDtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkNoZWNrIiwiQ2hlY2tDaGVjayIsIlVzZXJzIiwiQ2lyY2xlIiwidXNlQ2hhdCIsInVzZUF1dGgiLCJmb3JtYXRUaW1lIiwiZ2VuZXJhdGVBdmF0YXIiLCJDaGF0TGlzdCIsImNoYXRzIiwiYWN0aXZlQ2hhdCIsInNldEFjdGl2ZUNoYXQiLCJ1c2VyIiwiZ2V0TWVzc2FnZVN0YXR1cyIsIm1lc3NhZ2UiLCJzZW5kZXJfaWQiLCJpZCIsInN0YXR1cyIsImNsYXNzTmFtZSIsImxlbmd0aCIsImRpdiIsInAiLCJtYXAiLCJjaGF0Iiwib25DbGljayIsInN0eWxlIiwiYmFja2dyb3VuZENvbG9yIiwiYXZhdGFyIiwibmFtZSIsImltZyIsInNyYyIsImFsdCIsInR5cGUiLCJzcGFuIiwiY2hhckF0IiwidG9VcHBlckNhc2UiLCJpc19vbmxpbmUiLCJoMyIsImxhc3RfbWVzc2FnZSIsImNyZWF0ZWRfYXQiLCJ0cnVuY2F0ZU1lc3NhZ2UiLCJjb250ZW50IiwibGFzdF9zZWVuIiwidW5yZWFkX2NvdW50IiwibWF4TGVuZ3RoIiwic3Vic3RyaW5nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/chat/ChatList.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/chat/ConnectionStatus.tsx":
/*!**************************************************!*\
  !*** ./src/components/chat/ConnectionStatus.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst ConnectionStatus = ({ isConnected })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center space-x-1\",\n        children: isConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-3 h-3 text-green-400\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ConnectionStatus.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-xs opacity-75\",\n                    children: \"متصل\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ConnectionStatus.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"w-3 h-3 text-red-400\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ConnectionStatus.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-xs opacity-75\",\n                    children: \"غير متصل\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ConnectionStatus.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/ConnectionStatus.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ConnectionStatus);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9jaGF0L0Nvbm5lY3Rpb25TdGF0dXMudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRTBCO0FBQ21CO0FBTTdDLE1BQU1HLG1CQUFvRCxDQUFDLEVBQUVDLFdBQVcsRUFBRTtJQUN4RSxxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTtrQkFDWkYsNEJBQ0M7OzhCQUNFLDhEQUFDSCx3RkFBSUE7b0JBQUNLLFdBQVU7Ozs7Ozs4QkFDaEIsOERBQUNDO29CQUFLRCxXQUFVOzhCQUFxQjs7Ozs7Ozt5Q0FHdkM7OzhCQUNFLDhEQUFDSix3RkFBT0E7b0JBQUNJLFdBQVU7Ozs7Ozs4QkFDbkIsOERBQUNDO29CQUFLRCxXQUFVOzhCQUFxQjs7Ozs7Ozs7Ozs7OztBQUsvQztBQUVBLGlFQUFlSCxnQkFBZ0JBLEVBQUMiLCJzb3VyY2VzIjpbIi9ob21lL21hemVuL0Rlc2t0b3AvdGltZS1jaGF0L25leHRqcy1jaGF0L3NyYy9jb21wb25lbnRzL2NoYXQvQ29ubmVjdGlvblN0YXR1cy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgV2lmaSwgV2lmaU9mZiB9IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XG5cbmludGVyZmFjZSBDb25uZWN0aW9uU3RhdHVzUHJvcHMge1xuICBpc0Nvbm5lY3RlZDogYm9vbGVhbjtcbn1cblxuY29uc3QgQ29ubmVjdGlvblN0YXR1czogUmVhY3QuRkM8Q29ubmVjdGlvblN0YXR1c1Byb3BzPiA9ICh7IGlzQ29ubmVjdGVkIH0pID0+IHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMVwiPlxuICAgICAge2lzQ29ubmVjdGVkID8gKFxuICAgICAgICA8PlxuICAgICAgICAgIDxXaWZpIGNsYXNzTmFtZT1cInctMyBoLTMgdGV4dC1ncmVlbi00MDBcIiAvPlxuICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgb3BhY2l0eS03NVwiPtmF2KrYtdmEPC9zcGFuPlxuICAgICAgICA8Lz5cbiAgICAgICkgOiAoXG4gICAgICAgIDw+XG4gICAgICAgICAgPFdpZmlPZmYgY2xhc3NOYW1lPVwidy0zIGgtMyB0ZXh0LXJlZC00MDBcIiAvPlxuICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgb3BhY2l0eS03NVwiPti62YrYsSDZhdiq2LXZhDwvc3Bhbj5cbiAgICAgICAgPC8+XG4gICAgICApfVxuICAgIDwvZGl2PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgQ29ubmVjdGlvblN0YXR1cztcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIldpZmkiLCJXaWZpT2ZmIiwiQ29ubmVjdGlvblN0YXR1cyIsImlzQ29ubmVjdGVkIiwiZGl2IiwiY2xhc3NOYW1lIiwic3BhbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/chat/ConnectionStatus.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/chat/MessageBubble.tsx":
/*!***********************************************!*\
  !*** ./src/components/chat/MessageBubble.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Check_CheckCheck_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Check,CheckCheck,Clock!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Check_CheckCheck_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,CheckCheck,Clock!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_CheckCheck_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,CheckCheck,Clock!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-check.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst MessageBubble = ({ message, isOwnMessage, isGrouped, showAvatar })=>{\n    const getMessageStatus = ()=>{\n        if (!isOwnMessage) return null;\n        switch(message.status){\n            case 'sent':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CheckCheck_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"w-4 h-4 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageBubble.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 16\n                }, undefined);\n            case 'delivered':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CheckCheck_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-4 h-4 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageBubble.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 16\n                }, undefined);\n            case 'read':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CheckCheck_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-4 h-4 text-whatsapp-blue\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageBubble.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CheckCheck_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"w-4 h-4 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageBubble.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex items-end space-x-2 mb-1 message-slide-in ${isOwnMessage ? 'justify-end' : 'justify-start'} ${isGrouped ? 'mt-1' : 'mt-4'}`,\n        children: [\n            !isOwnMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `flex-shrink-0 ${showAvatar ? 'visible' : 'invisible'}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium\",\n                    style: {\n                        backgroundColor: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.generateAvatar)(message.sender?.username || 'User')\n                    },\n                    children: message.sender?.avatar_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: message.sender.avatar_url,\n                        alt: message.sender.username,\n                        className: \"w-full h-full rounded-full object-cover\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageBubble.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 15\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: message.sender?.username?.charAt(0).toUpperCase() || 'U'\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageBubble.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageBubble.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageBubble.tsx\",\n                lineNumber: 44,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `max-w-xs lg:max-w-md px-4 py-2 rounded-2xl shadow-sm relative ${isOwnMessage ? 'bg-whatsapp-green text-white rounded-br-md' : 'bg-white text-gray-900 rounded-bl-md border'} ${isGrouped ? 'rounded-t-2xl' : ''}`,\n                children: [\n                    !isOwnMessage && !isGrouped && message.sender && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs font-medium text-whatsapp-green mb-1\",\n                        children: message.sender.username\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageBubble.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"break-words whitespace-pre-wrap\",\n                        children: message.content\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageBubble.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `flex items-center justify-end mt-1 space-x-1 ${isOwnMessage ? 'text-white/70' : 'text-gray-500'}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs\",\n                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatTime)(message.created_at)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageBubble.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, undefined),\n                            getMessageStatus()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageBubble.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `absolute bottom-0 w-3 h-3 ${isOwnMessage ? 'right-0 bg-whatsapp-green transform rotate-45 translate-x-1 translate-y-1' : 'left-0 bg-white border-l border-b transform rotate-45 -translate-x-1 translate-y-1'} ${isGrouped ? 'hidden' : 'block'}`\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageBubble.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageBubble.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageBubble.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MessageBubble);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/chat/MessageBubble.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/chat/MessageInput.tsx":
/*!**********************************************!*\
  !*** ./src/components/chat/MessageInput.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_File_Image_Mic_Paperclip_Send_Smile_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=File,Image,Mic,Paperclip,Send,Smile!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/paperclip.js\");\n/* harmony import */ var _barrel_optimize_names_File_Image_Mic_Paperclip_Send_Smile_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=File,Image,Mic,Paperclip,Send,Smile!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_File_Image_Mic_Paperclip_Send_Smile_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=File,Image,Mic,Paperclip,Send,Smile!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var _barrel_optimize_names_File_Image_Mic_Paperclip_Send_Smile_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=File,Image,Mic,Paperclip,Send,Smile!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/smile.js\");\n/* harmony import */ var _barrel_optimize_names_File_Image_Mic_Paperclip_Send_Smile_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=File,Image,Mic,Paperclip,Send,Smile!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_File_Image_Mic_Paperclip_Send_Smile_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=File,Image,Mic,Paperclip,Send,Smile!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _context_ChatContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/ChatContext */ \"(ssr)/./src/context/ChatContext.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst MessageInput = ()=>{\n    const { sendMessage, sendTyping, activeChat } = (0,_context_ChatContext__WEBPACK_IMPORTED_MODULE_2__.useChat)();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAttachMenu, setShowAttachMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Debounced typing indicator\n    const debouncedSendTyping = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.debounce)(()=>{\n        if (message.trim()) {\n            sendTyping();\n        }\n    }, 500);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MessageInput.useEffect\": ()=>{\n            if (message.trim()) {\n                debouncedSendTyping();\n            }\n        }\n    }[\"MessageInput.useEffect\"], [\n        message,\n        debouncedSendTyping\n    ]);\n    // Auto-resize textarea\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MessageInput.useEffect\": ()=>{\n            if (textareaRef.current) {\n                textareaRef.current.style.height = 'auto';\n                textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 120)}px`;\n            }\n        }\n    }[\"MessageInput.useEffect\"], [\n        message\n    ]);\n    const handleSend = async ()=>{\n        if (!message.trim() || !activeChat) return;\n        try {\n            await sendMessage(message.trim());\n            setMessage('');\n            if (textareaRef.current) {\n                textareaRef.current.style.height = 'auto';\n            }\n        } catch (error) {\n            console.error('Error sending message:', error);\n        // You could show an error toast here\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleSend();\n        }\n    };\n    const handleAttachment = (type)=>{\n        setShowAttachMenu(false);\n        // Handle different attachment types\n        console.log('Attachment type:', type);\n    // You can implement file upload logic here\n    };\n    const toggleRecording = ()=>{\n        setIsRecording(!isRecording);\n    // Implement voice recording logic here\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white border-t border-gray-200 p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-end space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowAttachMenu(!showAttachMenu),\n                                className: \"p-3 text-gray-500 hover:text-whatsapp-green hover:bg-gray-100 rounded-full transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_File_Image_Mic_Paperclip_Send_Smile_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageInput.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageInput.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 11\n                            }, undefined),\n                            showAttachMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-12 right-0 bg-white rounded-lg shadow-lg border py-2 w-48 z-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleAttachment('image'),\n                                        className: \"w-full text-right px-4 py-2 text-gray-700 hover:bg-gray-100 flex items-center transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_File_Image_Mic_Paperclip_Send_Smile_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-4 h-4 ml-3 text-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageInput.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"صورة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageInput.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleAttachment('file'),\n                                        className: \"w-full text-right px-4 py-2 text-gray-700 hover:bg-gray-100 flex items-center transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_File_Image_Mic_Paperclip_Send_Smile_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-4 h-4 ml-3 text-green-500\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageInput.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"ملف\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageInput.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageInput.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageInput.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 relative\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-end bg-gray-50 rounded-2xl border border-gray-200 focus-within:border-whatsapp-green transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"p-3 text-gray-500 hover:text-whatsapp-green transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_File_Image_Mic_Paperclip_Send_Smile_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageInput.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageInput.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    ref: textareaRef,\n                                    value: message,\n                                    onChange: (e)=>setMessage(e.target.value),\n                                    onKeyPress: handleKeyPress,\n                                    placeholder: \"اكتب رسالة...\",\n                                    className: \"flex-1 bg-transparent py-3 px-2 outline-none resize-none max-h-[120px] min-h-[24px]\",\n                                    rows: 1\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageInput.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageInput.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageInput.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, undefined),\n                    message.trim() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleSend,\n                        className: \"p-3 bg-whatsapp-green hover:bg-whatsapp-green-dark text-white rounded-full transition-colors btn-hover\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_File_Image_Mic_Paperclip_Send_Smile_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"w-5 h-5\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageInput.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageInput.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: toggleRecording,\n                        className: `p-3 rounded-full transition-colors btn-hover ${isRecording ? 'bg-red-500 hover:bg-red-600 text-white animate-pulse' : 'bg-whatsapp-green hover:bg-whatsapp-green-dark text-white'}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_File_Image_Mic_Paperclip_Send_Smile_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"w-5 h-5\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageInput.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageInput.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageInput.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, undefined),\n            isRecording && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-2 flex items-center justify-center space-x-2 text-red-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-2 h-2 bg-red-500 rounded-full animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageInput.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm\",\n                        children: \"جاري التسجيل...\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageInput.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageInput.tsx\",\n                lineNumber: 148,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageInput.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MessageInput);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/chat/MessageInput.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/chat/MessageList.tsx":
/*!*********************************************!*\
  !*** ./src/components/chat/MessageList.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/AuthContext */ \"(ssr)/./src/context/AuthContext.tsx\");\n/* harmony import */ var _MessageBubble__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MessageBubble */ \"(ssr)/./src/components/chat/MessageBubble.tsx\");\n/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/LoadingSpinner */ \"(ssr)/./src/components/ui/LoadingSpinner.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst MessageList = ({ messages, isLoading })=>{\n    const { user } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MessageList.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"MessageList.useEffect\"], [\n        messages\n    ]);\n    const scrollToBottom = ()=>{\n        messagesEndRef.current?.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    const shouldShowDateSeparator = (currentMessage, previousMessage)=>{\n        if (!previousMessage) return true;\n        const currentDate = new Date(currentMessage.created_at).toDateString();\n        const previousDate = new Date(previousMessage.created_at).toDateString();\n        return currentDate !== previousDate;\n    };\n    const shouldGroupMessage = (currentMessage, previousMessage)=>{\n        if (!previousMessage) return false;\n        const timeDiff = new Date(currentMessage.created_at).getTime() - new Date(previousMessage.created_at).getTime();\n        const fiveMinutes = 5 * 60 * 1000;\n        return currentMessage.sender_id === previousMessage.sender_id && timeDiff < fiveMinutes;\n    };\n    if (isLoading && messages.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                size: \"large\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageList.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageList.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (messages.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-full text-gray-500\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-4\",\n                        children: \"\\uD83D\\uDCAC\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageList.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg font-medium mb-2\",\n                        children: \"لا توجد رسائل بعد\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageList.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm\",\n                        children: \"ابدأ المحادثة بإرسال رسالة\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageList.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageList.tsx\",\n                lineNumber: 59,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageList.tsx\",\n            lineNumber: 58,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-4 space-y-1\",\n        children: [\n            messages.map((message, index)=>{\n                const previousMessage = index > 0 ? messages[index - 1] : undefined;\n                const showDateSeparator = shouldShowDateSeparator(message, previousMessage);\n                const isGrouped = shouldGroupMessage(message, previousMessage);\n                const isOwnMessage = message.sender_id === user?.id;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        showDateSeparator && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center my-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-200 text-gray-600 text-xs px-3 py-1 rounded-full\",\n                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatDate)(message.created_at)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageList.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageList.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageBubble__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            message: message,\n                            isOwnMessage: isOwnMessage,\n                            isGrouped: isGrouped,\n                            showAvatar: !isOwnMessage && !isGrouped\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageList.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, message.id, true, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageList.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 11\n                }, undefined);\n            }),\n            isLoading && messages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center py-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    size: \"small\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageList.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageList.tsx\",\n                lineNumber: 100,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: messagesEndRef\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageList.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/MessageList.tsx\",\n        lineNumber: 69,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MessageList);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/chat/MessageList.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/chat/NotificationPanel.tsx":
/*!***************************************************!*\
  !*** ./src/components/chat/NotificationPanel.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_MessageCircle_Trash2_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,Trash2,UserCheck,UserX,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_Trash2_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,Trash2,UserCheck,UserX,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_Trash2_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,Trash2,UserCheck,UserX,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user-x.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_Trash2_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,Trash2,UserCheck,UserX,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_Trash2_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,Trash2,UserCheck,UserX,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _context_ChatContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/ChatContext */ \"(ssr)/./src/context/ChatContext.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst NotificationPanel = ({ onClose })=>{\n    const { notifications, clearNotifications, markNotificationAsRead } = (0,_context_ChatContext__WEBPACK_IMPORTED_MODULE_2__.useChat)();\n    const getNotificationIcon = (type)=>{\n        switch(type){\n            case 'message':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_Trash2_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-4 h-4 text-whatsapp-green\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/NotificationPanel.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 16\n                }, undefined);\n            case 'user_online':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_Trash2_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-4 h-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/NotificationPanel.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 16\n                }, undefined);\n            case 'user_offline':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_Trash2_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-4 h-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/NotificationPanel.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_Trash2_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-4 h-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/NotificationPanel.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-xl border w-80 max-h-96 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-4 border-b bg-gray-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-gray-900\",\n                        children: \"الإشعارات\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/NotificationPanel.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            notifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: clearNotifications,\n                                className: \"p-1 text-gray-500 hover:text-red-500 transition-colors\",\n                                title: \"مسح جميع الإشعارات\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_Trash2_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/NotificationPanel.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/NotificationPanel.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"p-1 text-gray-500 hover:text-gray-700 transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_Trash2_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/NotificationPanel.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/NotificationPanel.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/NotificationPanel.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/NotificationPanel.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-h-80 overflow-y-auto\",\n                children: notifications.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-8 text-center text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_Trash2_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-8 h-8 mx-auto mb-2 opacity-50\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/NotificationPanel.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"لا توجد إشعارات\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/NotificationPanel.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/NotificationPanel.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"divide-y divide-gray-100\",\n                    children: notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `p-4 hover:bg-gray-50 transition-colors cursor-pointer ${!notification.read ? 'bg-blue-50 border-r-2 border-whatsapp-green' : ''}`,\n                            onClick: ()=>markNotificationAsRead(notification.id),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0 mt-1\",\n                                        children: getNotificationIcon(notification.type)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/NotificationPanel.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-900 truncate\",\n                                                        children: notification.title\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/NotificationPanel.tsx\",\n                                                        lineNumber: 75,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatTime)(notification.timestamp.toISOString())\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/NotificationPanel.tsx\",\n                                                        lineNumber: 78,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/NotificationPanel.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mt-1 line-clamp-2\",\n                                                children: notification.message\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/NotificationPanel.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/NotificationPanel.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/NotificationPanel.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 17\n                            }, undefined)\n                        }, notification.id, false, {\n                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/NotificationPanel.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 15\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/NotificationPanel.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/NotificationPanel.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/NotificationPanel.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NotificationPanel);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/chat/NotificationPanel.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/chat/Sidebar.tsx":
/*!*****************************************!*\
  !*** ./src/components/chat/Sidebar.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bell_BellOff_LogOut_MessageCircle_MoreVertical_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BellOff,LogOut,MessageCircle,MoreVertical,Search,Settings,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BellOff_LogOut_MessageCircle_MoreVertical_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BellOff,LogOut,MessageCircle,MoreVertical,Search,Settings,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell-off.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BellOff_LogOut_MessageCircle_MoreVertical_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BellOff,LogOut,MessageCircle,MoreVertical,Search,Settings,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/ellipsis-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BellOff_LogOut_MessageCircle_MoreVertical_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BellOff,LogOut,MessageCircle,MoreVertical,Search,Settings,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BellOff_LogOut_MessageCircle_MoreVertical_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BellOff,LogOut,MessageCircle,MoreVertical,Search,Settings,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BellOff_LogOut_MessageCircle_MoreVertical_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BellOff,LogOut,MessageCircle,MoreVertical,Search,Settings,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BellOff_LogOut_MessageCircle_MoreVertical_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BellOff,LogOut,MessageCircle,MoreVertical,Search,Settings,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BellOff_LogOut_MessageCircle_MoreVertical_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BellOff,LogOut,MessageCircle,MoreVertical,Search,Settings,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/AuthContext */ \"(ssr)/./src/context/AuthContext.tsx\");\n/* harmony import */ var _context_ChatContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/context/ChatContext */ \"(ssr)/./src/context/ChatContext.tsx\");\n/* harmony import */ var _ChatList__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ChatList */ \"(ssr)/./src/components/chat/ChatList.tsx\");\n/* harmony import */ var _ConnectionStatus__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ConnectionStatus */ \"(ssr)/./src/components/chat/ConnectionStatus.tsx\");\n/* harmony import */ var _NotificationPanel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./NotificationPanel */ \"(ssr)/./src/components/chat/NotificationPanel.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nconst Sidebar = ()=>{\n    const { user, logout } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { chats, notifications, isConnected } = (0,_context_ChatContext__WEBPACK_IMPORTED_MODULE_3__.useChat)();\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showMenu, setShowMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showNotifications, setShowNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const filteredChats = chats.filter((chat)=>chat.name.toLowerCase().includes(searchQuery.toLowerCase()));\n    const unreadNotifications = notifications.filter((n)=>!n.read).length;\n    const handleLogout = ()=>{\n        logout();\n        setShowMenu(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-whatsapp-teal text-white p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-white rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-whatsapp-teal font-semibold text-lg\",\n                                        children: user?.username.charAt(0).toUpperCase()\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/Sidebar.tsx\",\n                                        lineNumber: 36,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/Sidebar.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"font-semibold\",\n                                            children: user?.username\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/Sidebar.tsx\",\n                                            lineNumber: 41,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ConnectionStatus__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            isConnected: isConnected\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/Sidebar.tsx\",\n                                            lineNumber: 42,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/Sidebar.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/Sidebar.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowNotifications(!showNotifications),\n                                            className: \"p-2 hover:bg-whatsapp-green-dark rounded-full transition-colors relative\",\n                                            children: [\n                                                unreadNotifications > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_BellOff_LogOut_MessageCircle_MoreVertical_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/Sidebar.tsx\",\n                                                    lineNumber: 53,\n                                                    columnNumber: 44\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_BellOff_LogOut_MessageCircle_MoreVertical_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/Sidebar.tsx\",\n                                                    lineNumber: 53,\n                                                    columnNumber: 75\n                                                }, undefined),\n                                                unreadNotifications > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\",\n                                                    children: unreadNotifications > 9 ? '9+' : unreadNotifications\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/Sidebar.tsx\",\n                                                    lineNumber: 55,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/Sidebar.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        showNotifications && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute left-0 top-12 z-50\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NotificationPanel__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                onClose: ()=>setShowNotifications(false)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/Sidebar.tsx\",\n                                                lineNumber: 63,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/Sidebar.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/Sidebar.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowMenu(!showMenu),\n                                            className: \"p-2 hover:bg-whatsapp-green-dark rounded-full transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_BellOff_LogOut_MessageCircle_MoreVertical_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/Sidebar.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/Sidebar.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        showMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute left-0 top-12 bg-white rounded-lg shadow-lg py-2 w-48 z-10\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"w-full text-right px-4 py-2 text-gray-700 hover:bg-gray-100 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_BellOff_LogOut_MessageCircle_MoreVertical_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"w-4 h-4 ml-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/Sidebar.tsx\",\n                                                            lineNumber: 80,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"الإعدادات\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/Sidebar.tsx\",\n                                                    lineNumber: 79,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleLogout,\n                                                    className: \"w-full text-right px-4 py-2 text-red-600 hover:bg-gray-100 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_BellOff_LogOut_MessageCircle_MoreVertical_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-4 h-4 ml-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/Sidebar.tsx\",\n                                                            lineNumber: 87,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"تسجيل الخروج\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/Sidebar.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/Sidebar.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/Sidebar.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/Sidebar.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/Sidebar.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/Sidebar.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-gray-50 border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_BellOff_LogOut_MessageCircle_MoreVertical_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/Sidebar.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            placeholder: \"البحث في المحادثات...\",\n                            value: searchQuery,\n                            onChange: (e)=>setSearchQuery(e.target.value),\n                            className: \"w-full pl-4 pr-10 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-whatsapp-green transition-colors\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/Sidebar.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/Sidebar.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/Sidebar.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatList__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    chats: filteredChats\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/Sidebar.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/Sidebar.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-gray-50 border-t\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"p-2 text-gray-600 hover:text-whatsapp-green transition-colors rounded-full hover:bg-gray-100\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_BellOff_LogOut_MessageCircle_MoreVertical_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/Sidebar.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/Sidebar.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"p-2 text-gray-600 hover:text-whatsapp-green transition-colors rounded-full hover:bg-gray-100\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_BellOff_LogOut_MessageCircle_MoreVertical_Search_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/Sidebar.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/Sidebar.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/Sidebar.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/Sidebar.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/Sidebar.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Sidebar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9jaGF0L1NpZGViYXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFd0M7QUFDbUU7QUFDM0Q7QUFDQTtBQUNkO0FBQ2dCO0FBQ0U7QUFFcEQsTUFBTWUsVUFBb0I7SUFDeEIsTUFBTSxFQUFFQyxJQUFJLEVBQUVDLE1BQU0sRUFBRSxHQUFHUCw2REFBT0E7SUFDaEMsTUFBTSxFQUFFUSxLQUFLLEVBQUVDLGFBQWEsRUFBRUMsV0FBVyxFQUFFLEdBQUdULDZEQUFPQTtJQUNyRCxNQUFNLENBQUNVLGFBQWFDLGVBQWUsR0FBR3JCLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ3NCLFVBQVVDLFlBQVksR0FBR3ZCLCtDQUFRQSxDQUFDO0lBQ3pDLE1BQU0sQ0FBQ3dCLG1CQUFtQkMscUJBQXFCLEdBQUd6QiwrQ0FBUUEsQ0FBQztJQUUzRCxNQUFNMEIsZ0JBQWdCVCxNQUFNVSxNQUFNLENBQUNDLENBQUFBLE9BQ2pDQSxLQUFLQyxJQUFJLENBQUNDLFdBQVcsR0FBR0MsUUFBUSxDQUFDWCxZQUFZVSxXQUFXO0lBRzFELE1BQU1FLHNCQUFzQmQsY0FBY1MsTUFBTSxDQUFDTSxDQUFBQSxJQUFLLENBQUNBLEVBQUVDLElBQUksRUFBRUMsTUFBTTtJQUVyRSxNQUFNQyxlQUFlO1FBQ25CcEI7UUFDQU8sWUFBWTtJQUNkO0lBRUEscUJBQ0UsOERBQUNjO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDQzt3Q0FBS0QsV0FBVTtrREFDYnZCLE1BQU15QixTQUFTQyxPQUFPLEdBQUdDOzs7Ozs7Ozs7Ozs4Q0FHOUIsOERBQUNMOztzREFDQyw4REFBQ007NENBQUdMLFdBQVU7c0RBQWlCdkIsTUFBTXlCOzs7Ozs7c0RBQ3JDLDhEQUFDNUIseURBQWdCQTs0Q0FBQ08sYUFBYUE7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FJbkMsOERBQUNrQjs0QkFBSUMsV0FBVTs7OENBRWIsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ007NENBQ0NDLFNBQVMsSUFBTXBCLHFCQUFxQixDQUFDRDs0Q0FDckNjLFdBQVU7O2dEQUVUTixzQkFBc0Isa0JBQUksOERBQUN6QixnSkFBSUE7b0RBQUMrQixXQUFVOzs7Ozs4RUFBZSw4REFBQzlCLGdKQUFPQTtvREFBQzhCLFdBQVU7Ozs7OztnREFDNUVOLHNCQUFzQixtQkFDckIsOERBQUNPO29EQUFLRCxXQUFVOzhEQUNiTixzQkFBc0IsSUFBSSxPQUFPQTs7Ozs7Ozs7Ozs7O3dDQUt2Q1IsbUNBQ0MsOERBQUNhOzRDQUFJQyxXQUFVO3NEQUNiLDRFQUFDekIsMERBQWlCQTtnREFBQ2lDLFNBQVMsSUFBTXJCLHFCQUFxQjs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBTTdELDhEQUFDWTtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNNOzRDQUNDQyxTQUFTLElBQU10QixZQUFZLENBQUNEOzRDQUM1QmdCLFdBQVU7c0RBRVYsNEVBQUNwQyxnSkFBWUE7Z0RBQUNvQyxXQUFVOzs7Ozs7Ozs7Ozt3Q0FHekJoQiwwQkFDQyw4REFBQ2U7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDTTtvREFBT04sV0FBVTs7c0VBQ2hCLDhEQUFDakMsaUpBQVFBOzREQUFDaUMsV0FBVTs7Ozs7O3dEQUFpQjs7Ozs7Ozs4REFHdkMsOERBQUNNO29EQUNDQyxTQUFTVDtvREFDVEUsV0FBVTs7c0VBRVYsOERBQUNoQyxpSkFBTUE7NERBQUNnQyxXQUFVOzs7Ozs7d0RBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBV2pELDhEQUFDRDtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDckMsaUpBQU1BOzRCQUFDcUMsV0FBVTs7Ozs7O3NDQUNsQiw4REFBQ1M7NEJBQ0NDLE1BQUs7NEJBQ0xDLGFBQVk7NEJBQ1pDLE9BQU85Qjs0QkFDUCtCLFVBQVUsQ0FBQ0MsSUFBTS9CLGVBQWUrQixFQUFFQyxNQUFNLENBQUNILEtBQUs7NEJBQzlDWixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OzswQkFNaEIsOERBQUNEO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDM0IsaURBQVFBO29CQUFDTSxPQUFPUzs7Ozs7Ozs7Ozs7MEJBSW5CLDhEQUFDVztnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDTTs0QkFBT04sV0FBVTtzQ0FDaEIsNEVBQUNuQyxpSkFBYUE7Z0NBQUNtQyxXQUFVOzs7Ozs7Ozs7OztzQ0FFM0IsOERBQUNNOzRCQUFPTixXQUFVO3NDQUNoQiw0RUFBQ2xDLGlKQUFLQTtnQ0FBQ2tDLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNN0I7QUFFQSxpRUFBZXhCLE9BQU9BLEVBQUMiLCJzb3VyY2VzIjpbIi9ob21lL21hemVuL0Rlc2t0b3AvdGltZS1jaGF0L25leHRqcy1jaGF0L3NyYy9jb21wb25lbnRzL2NoYXQvU2lkZWJhci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBTZWFyY2gsIE1vcmVWZXJ0aWNhbCwgTWVzc2FnZUNpcmNsZSwgVXNlcnMsIFNldHRpbmdzLCBMb2dPdXQsIEJlbGwsIEJlbGxPZmYgfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuaW1wb3J0IHsgdXNlQXV0aCB9IGZyb20gJ0AvY29udGV4dC9BdXRoQ29udGV4dCc7XG5pbXBvcnQgeyB1c2VDaGF0IH0gZnJvbSAnQC9jb250ZXh0L0NoYXRDb250ZXh0JztcbmltcG9ydCBDaGF0TGlzdCBmcm9tICcuL0NoYXRMaXN0JztcbmltcG9ydCBDb25uZWN0aW9uU3RhdHVzIGZyb20gJy4vQ29ubmVjdGlvblN0YXR1cyc7XG5pbXBvcnQgTm90aWZpY2F0aW9uUGFuZWwgZnJvbSAnLi9Ob3RpZmljYXRpb25QYW5lbCc7XG5cbmNvbnN0IFNpZGViYXI6IFJlYWN0LkZDID0gKCkgPT4ge1xuICBjb25zdCB7IHVzZXIsIGxvZ291dCB9ID0gdXNlQXV0aCgpO1xuICBjb25zdCB7IGNoYXRzLCBub3RpZmljYXRpb25zLCBpc0Nvbm5lY3RlZCB9ID0gdXNlQ2hhdCgpO1xuICBjb25zdCBbc2VhcmNoUXVlcnksIHNldFNlYXJjaFF1ZXJ5XSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW3Nob3dNZW51LCBzZXRTaG93TWVudV0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzaG93Tm90aWZpY2F0aW9ucywgc2V0U2hvd05vdGlmaWNhdGlvbnNdID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIGNvbnN0IGZpbHRlcmVkQ2hhdHMgPSBjaGF0cy5maWx0ZXIoY2hhdCA9PlxuICAgIGNoYXQubmFtZS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFF1ZXJ5LnRvTG93ZXJDYXNlKCkpXG4gICk7XG5cbiAgY29uc3QgdW5yZWFkTm90aWZpY2F0aW9ucyA9IG5vdGlmaWNhdGlvbnMuZmlsdGVyKG4gPT4gIW4ucmVhZCkubGVuZ3RoO1xuXG4gIGNvbnN0IGhhbmRsZUxvZ291dCA9ICgpID0+IHtcbiAgICBsb2dvdXQoKTtcbiAgICBzZXRTaG93TWVudShmYWxzZSk7XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaC1mdWxsXCI+XG4gICAgICB7LyogSGVhZGVyICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGF0c2FwcC10ZWFsIHRleHQtd2hpdGUgcC00XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMCBoLTEwIGJnLXdoaXRlIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoYXRzYXBwLXRlYWwgZm9udC1zZW1pYm9sZCB0ZXh0LWxnXCI+XG4gICAgICAgICAgICAgICAge3VzZXI/LnVzZXJuYW1lLmNoYXJBdCgwKS50b1VwcGVyQ2FzZSgpfVxuICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkXCI+e3VzZXI/LnVzZXJuYW1lfTwvaDI+XG4gICAgICAgICAgICAgIDxDb25uZWN0aW9uU3RhdHVzIGlzQ29ubmVjdGVkPXtpc0Nvbm5lY3RlZH0gLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICB7LyogTm90aWZpY2F0aW9ucyAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dOb3RpZmljYXRpb25zKCFzaG93Tm90aWZpY2F0aW9ucyl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0yIGhvdmVyOmJnLXdoYXRzYXBwLWdyZWVuLWRhcmsgcm91bmRlZC1mdWxsIHRyYW5zaXRpb24tY29sb3JzIHJlbGF0aXZlXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHt1bnJlYWROb3RpZmljYXRpb25zID4gMCA/IDxCZWxsIGNsYXNzTmFtZT1cInctNSBoLTVcIiAvPiA6IDxCZWxsT2ZmIGNsYXNzTmFtZT1cInctNSBoLTVcIiAvPn1cbiAgICAgICAgICAgICAgICB7dW5yZWFkTm90aWZpY2F0aW9ucyA+IDAgJiYgKFxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYWJzb2x1dGUgLXRvcC0xIC1yaWdodC0xIGJnLXJlZC01MDAgdGV4dC13aGl0ZSB0ZXh0LXhzIHJvdW5kZWQtZnVsbCB3LTUgaC01IGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgIHt1bnJlYWROb3RpZmljYXRpb25zID4gOSA/ICc5KycgOiB1bnJlYWROb3RpZmljYXRpb25zfVxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAge3Nob3dOb3RpZmljYXRpb25zICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGxlZnQtMCB0b3AtMTIgei01MFwiPlxuICAgICAgICAgICAgICAgICAgPE5vdGlmaWNhdGlvblBhbmVsIG9uQ2xvc2U9eygpID0+IHNldFNob3dOb3RpZmljYXRpb25zKGZhbHNlKX0gLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogTWVudSAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dNZW51KCFzaG93TWVudSl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0yIGhvdmVyOmJnLXdoYXRzYXBwLWdyZWVuLWRhcmsgcm91bmRlZC1mdWxsIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxNb3JlVmVydGljYWwgY2xhc3NOYW1lPVwidy01IGgtNVwiIC8+XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAge3Nob3dNZW51ICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGxlZnQtMCB0b3AtMTIgYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3ctbGcgcHktMiB3LTQ4IHotMTBcIj5cbiAgICAgICAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwidy1mdWxsIHRleHQtcmlnaHQgcHgtNCBweS0yIHRleHQtZ3JheS03MDAgaG92ZXI6YmctZ3JheS0xMDAgZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgPFNldHRpbmdzIGNsYXNzTmFtZT1cInctNCBoLTQgbWwtM1wiIC8+XG4gICAgICAgICAgICAgICAgICAgINin2YTYpdi52K/Yp9iv2KfYqlxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICA8YnV0dG9uIFxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVMb2dvdXR9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCB0ZXh0LXJpZ2h0IHB4LTQgcHktMiB0ZXh0LXJlZC02MDAgaG92ZXI6YmctZ3JheS0xMDAgZmxleCBpdGVtcy1jZW50ZXJcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8TG9nT3V0IGNsYXNzTmFtZT1cInctNCBoLTQgbWwtM1wiIC8+XG4gICAgICAgICAgICAgICAgICAgINiq2LPYrNmK2YQg2KfZhNiu2LHZiNisXG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogU2VhcmNoICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTQgYmctZ3JheS01MCBib3JkZXItYlwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgPFNlYXJjaCBjbGFzc05hbWU9XCJhYnNvbHV0ZSByaWdodC0zIHRvcC0xLzIgdHJhbnNmb3JtIC10cmFuc2xhdGUteS0xLzIgdGV4dC1ncmF5LTQwMCB3LTQgaC00XCIgLz5cbiAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi2KfZhNio2K3YqyDZgdmKINin2YTZhdit2KfYr9ir2KfYqi4uLlwiXG4gICAgICAgICAgICB2YWx1ZT17c2VhcmNoUXVlcnl9XG4gICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNlYXJjaFF1ZXJ5KGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBwbC00IHByLTEwIHB5LTIgYmctd2hpdGUgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCByb3VuZGVkLWxnIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy13aGF0c2FwcC1ncmVlbiB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgLz5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIENoYXQgTGlzdCAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG92ZXJmbG93LXktYXV0b1wiPlxuICAgICAgICA8Q2hhdExpc3QgY2hhdHM9e2ZpbHRlcmVkQ2hhdHN9IC8+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIEZvb3RlciAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00IGJnLWdyYXktNTAgYm9yZGVyLXRcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwicC0yIHRleHQtZ3JheS02MDAgaG92ZXI6dGV4dC13aGF0c2FwcC1ncmVlbiB0cmFuc2l0aW9uLWNvbG9ycyByb3VuZGVkLWZ1bGwgaG92ZXI6YmctZ3JheS0xMDBcIj5cbiAgICAgICAgICAgIDxNZXNzYWdlQ2lyY2xlIGNsYXNzTmFtZT1cInctNSBoLTVcIiAvPlxuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwicC0yIHRleHQtZ3JheS02MDAgaG92ZXI6dGV4dC13aGF0c2FwcC1ncmVlbiB0cmFuc2l0aW9uLWNvbG9ycyByb3VuZGVkLWZ1bGwgaG92ZXI6YmctZ3JheS0xMDBcIj5cbiAgICAgICAgICAgIDxVc2VycyBjbGFzc05hbWU9XCJ3LTUgaC01XCIgLz5cbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IFNpZGViYXI7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsIlNlYXJjaCIsIk1vcmVWZXJ0aWNhbCIsIk1lc3NhZ2VDaXJjbGUiLCJVc2VycyIsIlNldHRpbmdzIiwiTG9nT3V0IiwiQmVsbCIsIkJlbGxPZmYiLCJ1c2VBdXRoIiwidXNlQ2hhdCIsIkNoYXRMaXN0IiwiQ29ubmVjdGlvblN0YXR1cyIsIk5vdGlmaWNhdGlvblBhbmVsIiwiU2lkZWJhciIsInVzZXIiLCJsb2dvdXQiLCJjaGF0cyIsIm5vdGlmaWNhdGlvbnMiLCJpc0Nvbm5lY3RlZCIsInNlYXJjaFF1ZXJ5Iiwic2V0U2VhcmNoUXVlcnkiLCJzaG93TWVudSIsInNldFNob3dNZW51Iiwic2hvd05vdGlmaWNhdGlvbnMiLCJzZXRTaG93Tm90aWZpY2F0aW9ucyIsImZpbHRlcmVkQ2hhdHMiLCJmaWx0ZXIiLCJjaGF0IiwibmFtZSIsInRvTG93ZXJDYXNlIiwiaW5jbHVkZXMiLCJ1bnJlYWROb3RpZmljYXRpb25zIiwibiIsInJlYWQiLCJsZW5ndGgiLCJoYW5kbGVMb2dvdXQiLCJkaXYiLCJjbGFzc05hbWUiLCJzcGFuIiwidXNlcm5hbWUiLCJjaGFyQXQiLCJ0b1VwcGVyQ2FzZSIsImgyIiwiYnV0dG9uIiwib25DbGljayIsIm9uQ2xvc2UiLCJpbnB1dCIsInR5cGUiLCJwbGFjZWhvbGRlciIsInZhbHVlIiwib25DaGFuZ2UiLCJlIiwidGFyZ2V0Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/chat/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/chat/TypingIndicator.tsx":
/*!*************************************************!*\
  !*** ./src/components/chat/TypingIndicator.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/AuthContext */ \"(ssr)/./src/context/AuthContext.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst TypingIndicator = ({ typingUsers, activeChat })=>{\n    const { user } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    // Filter typing users for current chat and exclude current user\n    const relevantTypingUsers = typingUsers.filter((typingUser)=>{\n        if (typingUser.user_id === user?.id) return false;\n        if (activeChat.type === 'direct') {\n            return typingUser.recipient_id === user?.id || activeChat.user?.id === typingUser.user_id;\n        } else {\n            return typingUser.room_id === activeChat.room?.id;\n        }\n    });\n    if (relevantTypingUsers.length === 0) {\n        return null;\n    }\n    const getTypingText = ()=>{\n        const count = relevantTypingUsers.length;\n        if (count === 1) {\n            return `${relevantTypingUsers[0].username} يكتب...`;\n        } else if (count === 2) {\n            return `${relevantTypingUsers[0].username} و ${relevantTypingUsers[1].username} يكتبان...`;\n        } else {\n            return `${count} أشخاص يكتبون...`;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"px-4 pb-2\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [\n                activeChat.type === 'direct' && relevantTypingUsers.length === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-6 h-6 rounded-full flex items-center justify-center text-white text-xs font-medium flex-shrink-0\",\n                    style: {\n                        backgroundColor: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.generateAvatar)(relevantTypingUsers[0].username)\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: relevantTypingUsers[0].username.charAt(0).toUpperCase()\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/TypingIndicator.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/TypingIndicator.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl px-4 py-2 shadow-sm border flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-gray-600\",\n                            children: getTypingText()\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/TypingIndicator.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"typing-dot\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/TypingIndicator.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"typing-dot\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/TypingIndicator.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"typing-dot\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/TypingIndicator.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/TypingIndicator.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/TypingIndicator.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/TypingIndicator.tsx\",\n            lineNumber: 45,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/TypingIndicator.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TypingIndicator);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/chat/TypingIndicator.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/chat/WelcomeScreen.tsx":
/*!***********************************************!*\
  !*** ./src/components/chat/WelcomeScreen.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Globe_MessageCircle_Shield_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,MessageCircle,Shield,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_MessageCircle_Shield_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,MessageCircle,Shield,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_MessageCircle_Shield_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,MessageCircle,Shield,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_MessageCircle_Shield_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,MessageCircle,Shield,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_MessageCircle_Shield_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,MessageCircle,Shield,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst WelcomeScreen = ()=>{\n    const features = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_MessageCircle_Shield_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"w-6 h-6 text-yellow-500\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/WelcomeScreen.tsx\",\n                lineNumber: 9,\n                columnNumber: 13\n            }, undefined),\n            title: 'رسائل فورية',\n            description: 'تبادل الرسائل في الوقت الفعلي مع أصدقائك'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_MessageCircle_Shield_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                className: \"w-6 h-6 text-green-500\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/WelcomeScreen.tsx\",\n                lineNumber: 14,\n                columnNumber: 13\n            }, undefined),\n            title: 'آمن ومحمي',\n            description: 'محادثاتك محمية بأحدث تقنيات الأمان'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_MessageCircle_Shield_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"w-6 h-6 text-blue-500\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/WelcomeScreen.tsx\",\n                lineNumber: 19,\n                columnNumber: 13\n            }, undefined),\n            title: 'مجموعات',\n            description: 'أنشئ مجموعات وتحدث مع عدة أشخاص'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_MessageCircle_Shield_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"w-6 h-6 text-purple-500\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/WelcomeScreen.tsx\",\n                lineNumber: 24,\n                columnNumber: 13\n            }, undefined),\n            title: 'متاح في كل مكان',\n            description: 'استخدم التطبيق من أي جهاز ومن أي مكان'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 chat-bg flex items-center justify-center p-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center max-w-2xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-whatsapp-green rounded-full p-8 shadow-2xl animate-bounce-in\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_MessageCircle_Shield_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-20 h-20 text-white\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/WelcomeScreen.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/WelcomeScreen.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/WelcomeScreen.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-4xl font-bold text-gray-800 mb-4 animate-fade-in\",\n                    children: \"مرحباً بك في تايم شات\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/WelcomeScreen.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xl text-gray-600 mb-12 leading-relaxed animate-slide-up\",\n                    children: \"ابدأ محادثة جديدة مع أصدقائك واستمتع بتجربة دردشة رائعة ومميزة\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/WelcomeScreen.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-12\",\n                    children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 card-hover animate-fade-in\",\n                            style: {\n                                animationDelay: `${index * 0.1}s`\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: feature.icon\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/WelcomeScreen.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-gray-800 mb-1\",\n                                                children: feature.title\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/WelcomeScreen.tsx\",\n                                                lineNumber: 61,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 text-sm\",\n                                                children: feature.description\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/WelcomeScreen.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/WelcomeScreen.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/WelcomeScreen.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 15\n                            }, undefined)\n                        }, index, false, {\n                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/WelcomeScreen.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/WelcomeScreen.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl p-8 shadow-lg animate-slide-up\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-semibold text-gray-800 mb-4\",\n                            children: \"ابدأ الآن\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/WelcomeScreen.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-6\",\n                            children: \"اختر محادثة من القائمة الجانبية للبدء في المحادثة، أو ابحث عن أصدقائك الجدد\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/WelcomeScreen.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 text-whatsapp-green\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_MessageCircle_Shield_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/WelcomeScreen.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"محادثات مباشرة\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/WelcomeScreen.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/WelcomeScreen.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 text-whatsapp-green\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_MessageCircle_Shield_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/WelcomeScreen.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"مجموعات\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/WelcomeScreen.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/WelcomeScreen.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/WelcomeScreen.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/WelcomeScreen.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute top-20 left-20 w-32 h-32 bg-whatsapp-green opacity-10 rounded-full animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/WelcomeScreen.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute bottom-20 right-20 w-24 h-24 bg-whatsapp-blue opacity-10 rounded-full animate-pulse\",\n                    style: {\n                        animationDelay: '1s'\n                    }\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/WelcomeScreen.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/WelcomeScreen.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/chat/WelcomeScreen.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (WelcomeScreen);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/chat/WelcomeScreen.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/LoadingSpinner.tsx":
/*!**********************************************!*\
  !*** ./src/components/ui/LoadingSpinner.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst LoadingSpinner = ({ size = 'medium', className })=>{\n    const sizes = {\n        small: 'w-4 h-4',\n        medium: 'w-6 h-6',\n        large: 'w-8 h-8'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('animate-spin text-whatsapp-green', sizes[size], className)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/ui/LoadingSpinner.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/components/ui/LoadingSpinner.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LoadingSpinner);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9Mb2FkaW5nU3Bpbm5lci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBMEI7QUFDYTtBQUNOO0FBT2pDLE1BQU1HLGlCQUFnRCxDQUFDLEVBQ3JEQyxPQUFPLFFBQVEsRUFDZkMsU0FBUyxFQUNWO0lBQ0MsTUFBTUMsUUFBUTtRQUNaQyxPQUFPO1FBQ1BDLFFBQVE7UUFDUkMsT0FBTztJQUNUO0lBRUEscUJBQ0UsOERBQUNDO1FBQUlMLFdBQVU7a0JBQ2IsNEVBQUNKLG1GQUFPQTtZQUFDSSxXQUFXSCw4Q0FBRUEsQ0FBQyxvQ0FBb0NJLEtBQUssQ0FBQ0YsS0FBSyxFQUFFQzs7Ozs7Ozs7Ozs7QUFHOUU7QUFFQSxpRUFBZUYsY0FBY0EsRUFBQyIsInNvdXJjZXMiOlsiL2hvbWUvbWF6ZW4vRGVza3RvcC90aW1lLWNoYXQvbmV4dGpzLWNoYXQvc3JjL2NvbXBvbmVudHMvdWkvTG9hZGluZ1NwaW5uZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBMb2FkZXIyIH0gZnJvbSAnbHVjaWRlLXJlYWN0JztcbmltcG9ydCB7IGNuIH0gZnJvbSAnQC9saWIvdXRpbHMnO1xuXG5pbnRlcmZhY2UgTG9hZGluZ1NwaW5uZXJQcm9wcyB7XG4gIHNpemU/OiAnc21hbGwnIHwgJ21lZGl1bScgfCAnbGFyZ2UnO1xuICBjbGFzc05hbWU/OiBzdHJpbmc7XG59XG5cbmNvbnN0IExvYWRpbmdTcGlubmVyOiBSZWFjdC5GQzxMb2FkaW5nU3Bpbm5lclByb3BzPiA9ICh7XG4gIHNpemUgPSAnbWVkaXVtJyxcbiAgY2xhc3NOYW1lXG59KSA9PiB7XG4gIGNvbnN0IHNpemVzID0ge1xuICAgIHNtYWxsOiAndy00IGgtNCcsXG4gICAgbWVkaXVtOiAndy02IGgtNicsXG4gICAgbGFyZ2U6ICd3LTggaC04JyxcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgIDxMb2FkZXIyIGNsYXNzTmFtZT17Y24oJ2FuaW1hdGUtc3BpbiB0ZXh0LXdoYXRzYXBwLWdyZWVuJywgc2l6ZXNbc2l6ZV0sIGNsYXNzTmFtZSl9IC8+XG4gICAgPC9kaXY+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBMb2FkaW5nU3Bpbm5lcjtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkxvYWRlcjIiLCJjbiIsIkxvYWRpbmdTcGlubmVyIiwic2l6ZSIsImNsYXNzTmFtZSIsInNpemVzIiwic21hbGwiLCJtZWRpdW0iLCJsYXJnZSIsImRpdiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/LoadingSpinner.tsx\n");

/***/ }),

/***/ "(ssr)/./src/context/AuthContext.tsx":
/*!*************************************!*\
  !*** ./src/context/AuthContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var _lib_websocket__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/websocket */ \"(ssr)/./src/lib/websocket.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            initializeAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const initializeAuth = async ()=>{\n        try {\n            if (true) {\n                setIsLoading(false);\n                return;\n            }\n            const token = localStorage.getItem('access_token');\n            const savedUser = localStorage.getItem('user');\n            if (token && savedUser) {\n                setUser(JSON.parse(savedUser));\n                // Verify token is still valid\n                try {\n                    const currentUser = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiService.getCurrentUser();\n                    setUser(currentUser);\n                    localStorage.setItem('user', JSON.stringify(currentUser));\n                    // Connect to WebSocket\n                    await _lib_websocket__WEBPACK_IMPORTED_MODULE_3__.websocketService.connect(token);\n                } catch (error) {\n                    console.error('Token verification failed:', error);\n                    logout();\n                }\n            }\n        } catch (error) {\n            console.error('Auth initialization error:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const login = async (credentials)=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiService.login(credentials);\n            localStorage.setItem('access_token', response.access_token);\n            localStorage.setItem('user', JSON.stringify(response.user));\n            setUser(response.user);\n            // Connect to WebSocket\n            await _lib_websocket__WEBPACK_IMPORTED_MODULE_3__.websocketService.connect(response.access_token);\n        } catch (error) {\n            console.error('Login error:', error);\n            throw error;\n        }\n    };\n    const register = async (userData)=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiService.register(userData);\n            localStorage.setItem('access_token', response.access_token);\n            localStorage.setItem('user', JSON.stringify(response.user));\n            setUser(response.user);\n            // Connect to WebSocket\n            await _lib_websocket__WEBPACK_IMPORTED_MODULE_3__.websocketService.connect(response.access_token);\n        } catch (error) {\n            console.error('Registration error:', error);\n            throw error;\n        }\n    };\n    const logout = ()=>{\n        if (false) {}\n        setUser(null);\n        _lib_websocket__WEBPACK_IMPORTED_MODULE_3__.websocketService.disconnect();\n    };\n    const updateUser = async (userData)=>{\n        if (!user) return;\n        try {\n            const updatedUser = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiService.updateUser(user.id, userData);\n            setUser(updatedUser);\n            if (false) {}\n        } catch (error) {\n            console.error('Update user error:', error);\n            throw error;\n        }\n    };\n    const value = {\n        user,\n        isAuthenticated: !!user,\n        isLoading,\n        login,\n        register,\n        logout,\n        updateUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/context/AuthContext.tsx\",\n        lineNumber: 132,\n        columnNumber: 5\n    }, undefined);\n};\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/context/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/context/ChatContext.tsx":
/*!*************************************!*\
  !*** ./src/context/ChatContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatProvider: () => (/* binding */ ChatProvider),\n/* harmony export */   useChat: () => (/* binding */ useChat)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var _lib_websocket__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/websocket */ \"(ssr)/./src/lib/websocket.ts\");\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./AuthContext */ \"(ssr)/./src/context/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ ChatProvider,useChat auto */ \n\n\n\n\nconst ChatContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst ChatProvider = ({ children })=>{\n    const { user, isAuthenticated } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const [chats, setChats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [activeChat, setActiveChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [typingUsers, setTypingUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            if (isAuthenticated && user) {\n                initializeChat();\n                setupWebSocketListeners();\n            }\n            return ({\n                \"ChatProvider.useEffect\": ()=>{\n                    cleanupWebSocketListeners();\n                }\n            })[\"ChatProvider.useEffect\"];\n        }\n    }[\"ChatProvider.useEffect\"], [\n        isAuthenticated,\n        user\n    ]);\n    const initializeChat = async ()=>{\n        await refreshChats();\n    };\n    const setupWebSocketListeners = ()=>{\n        _lib_websocket__WEBPACK_IMPORTED_MODULE_3__.websocketService.on('connected', handleConnected);\n        _lib_websocket__WEBPACK_IMPORTED_MODULE_3__.websocketService.on('disconnected', handleDisconnected);\n        _lib_websocket__WEBPACK_IMPORTED_MODULE_3__.websocketService.on('message', handleNewMessage);\n        _lib_websocket__WEBPACK_IMPORTED_MODULE_3__.websocketService.on('typing', handleTyping);\n        _lib_websocket__WEBPACK_IMPORTED_MODULE_3__.websocketService.on('userStatus', handleUserStatus);\n        _lib_websocket__WEBPACK_IMPORTED_MODULE_3__.websocketService.on('messageRead', handleMessageRead);\n        _lib_websocket__WEBPACK_IMPORTED_MODULE_3__.websocketService.on('notification', handleNotification);\n    };\n    const cleanupWebSocketListeners = ()=>{\n        _lib_websocket__WEBPACK_IMPORTED_MODULE_3__.websocketService.off('connected', handleConnected);\n        _lib_websocket__WEBPACK_IMPORTED_MODULE_3__.websocketService.off('disconnected', handleDisconnected);\n        _lib_websocket__WEBPACK_IMPORTED_MODULE_3__.websocketService.off('message', handleNewMessage);\n        _lib_websocket__WEBPACK_IMPORTED_MODULE_3__.websocketService.off('typing', handleTyping);\n        _lib_websocket__WEBPACK_IMPORTED_MODULE_3__.websocketService.off('userStatus', handleUserStatus);\n        _lib_websocket__WEBPACK_IMPORTED_MODULE_3__.websocketService.off('messageRead', handleMessageRead);\n        _lib_websocket__WEBPACK_IMPORTED_MODULE_3__.websocketService.off('notification', handleNotification);\n    };\n    const handleConnected = ()=>{\n        setIsConnected(true);\n    };\n    const handleDisconnected = ()=>{\n        setIsConnected(false);\n    };\n    const handleNewMessage = (message)=>{\n        setMessages((prev)=>[\n                ...prev,\n                message\n            ]);\n        // Update chat list with new message\n        setChats((prev)=>prev.map((chat)=>{\n                if (chat.type === 'direct' && (chat.user?.id === message.sender_id && message.recipient_id === user?.id || chat.user?.id === message.recipient_id && message.sender_id === user?.id) || chat.type === 'room' && chat.room?.id === message.room_id) {\n                    return {\n                        ...chat,\n                        last_message: message,\n                        unread_count: chat.id === activeChat?.id ? 0 : chat.unread_count + 1\n                    };\n                }\n                return chat;\n            }));\n        // Add notification if message is not from current user and not in active chat\n        if (message.sender_id !== user?.id && (!activeChat || activeChat.type === 'direct' && activeChat.user?.id !== message.sender_id || activeChat.type === 'room' && activeChat.room?.id !== message.room_id)) {\n            const notification = {\n                id: `msg_${message.id}`,\n                type: 'message',\n                title: message.sender?.username || 'رسالة جديدة',\n                message: message.content,\n                timestamp: new Date(message.created_at),\n                read: false\n            };\n            setNotifications((prev)=>[\n                    notification,\n                    ...prev\n                ]);\n        }\n    };\n    const handleTyping = (typingData)=>{\n        setTypingUsers((prev)=>{\n            const filtered = prev.filter((t)=>t.user_id !== typingData.user_id);\n            return [\n                ...filtered,\n                typingData\n            ];\n        });\n        // Remove typing indicator after 3 seconds\n        setTimeout(()=>{\n            setTypingUsers((prev)=>prev.filter((t)=>t.user_id !== typingData.user_id));\n        }, 3000);\n    };\n    const handleUserStatus = (statusData)=>{\n        setChats((prev)=>prev.map((chat)=>{\n                if (chat.type === 'direct' && chat.user?.id === statusData.user_id) {\n                    return {\n                        ...chat,\n                        is_online: statusData.is_online,\n                        user: chat.user ? {\n                            ...chat.user,\n                            is_online: statusData.is_online\n                        } : undefined\n                    };\n                }\n                return chat;\n            }));\n        // Add notification for user status change\n        const notification = {\n            id: `status_${statusData.user_id}_${Date.now()}`,\n            type: statusData.is_online ? 'user_online' : 'user_offline',\n            title: 'حالة المستخدم',\n            message: statusData.is_online ? 'متصل الآن' : 'غير متصل',\n            timestamp: new Date(),\n            read: false\n        };\n        setNotifications((prev)=>[\n                notification,\n                ...prev.slice(0, 49)\n            ]); // Keep only last 50 notifications\n    };\n    const handleMessageRead = (data)=>{\n        setMessages((prev)=>prev.map((message)=>{\n                if (message.id === data.message_id) {\n                    return {\n                        ...message,\n                        status: 'read'\n                    };\n                }\n                return message;\n            }));\n    };\n    const handleNotification = (notificationData)=>{\n        const notification = {\n            id: `notif_${Date.now()}`,\n            type: notificationData.type || 'message',\n            title: notificationData.title || 'إشعار جديد',\n            message: notificationData.message || '',\n            timestamp: new Date(),\n            read: false\n        };\n        setNotifications((prev)=>[\n                notification,\n                ...prev.slice(0, 49)\n            ]);\n    };\n    const refreshChats = async ()=>{\n        if (!user) return;\n        setIsLoading(true);\n        try {\n            // Get direct message chats\n            const users = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiService.getUsers();\n            const directChats = users.items.filter((u)=>u.id !== user.id).map((u)=>({\n                    id: `direct_${u.id}`,\n                    type: 'direct',\n                    name: u.username,\n                    avatar: u.avatar_url,\n                    unread_count: 0,\n                    is_online: u.is_online,\n                    last_seen: u.last_seen,\n                    user: u\n                }));\n            // Get room chats\n            const rooms = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiService.getRooms();\n            const roomChats = rooms.items.map((room)=>({\n                    id: `room_${room.id}`,\n                    type: 'room',\n                    name: room.name,\n                    unread_count: 0,\n                    room: room\n                }));\n            setChats([\n                ...directChats,\n                ...roomChats\n            ]);\n        } catch (error) {\n            console.error('Error loading chats:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const loadMessages = async (chatId)=>{\n        setIsLoading(true);\n        try {\n            const [type, id] = chatId.split('_');\n            const numericId = parseInt(id);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiService.getMessages(1, 100, type === 'direct' ? numericId : undefined, type === 'room' ? numericId : undefined);\n            setMessages(response.items.reverse());\n        } catch (error) {\n            console.error('Error loading messages:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const sendMessage = async (content)=>{\n        if (!activeChat || !user) return;\n        try {\n            const [type, id] = activeChat.id.split('_');\n            const numericId = parseInt(id);\n            if (_lib_websocket__WEBPACK_IMPORTED_MODULE_3__.websocketService.isConnected()) {\n                _lib_websocket__WEBPACK_IMPORTED_MODULE_3__.websocketService.sendMessage(content, type === 'direct' ? numericId : undefined, type === 'room' ? numericId : undefined);\n            } else {\n                // Fallback to API if WebSocket is not connected\n                await _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiService.sendMessage({\n                    content,\n                    recipient_id: type === 'direct' ? numericId : undefined,\n                    room_id: type === 'room' ? numericId : undefined,\n                    message_type: type\n                });\n            }\n        } catch (error) {\n            console.error('Error sending message:', error);\n            throw error;\n        }\n    };\n    const markAsRead = (messageId)=>{\n        if (_lib_websocket__WEBPACK_IMPORTED_MODULE_3__.websocketService.isConnected()) {\n            _lib_websocket__WEBPACK_IMPORTED_MODULE_3__.websocketService.markMessageAsRead(messageId);\n        }\n    };\n    const sendTyping = ()=>{\n        if (!activeChat || !_lib_websocket__WEBPACK_IMPORTED_MODULE_3__.websocketService.isConnected()) return;\n        const [type, id] = activeChat.id.split('_');\n        const numericId = parseInt(id);\n        _lib_websocket__WEBPACK_IMPORTED_MODULE_3__.websocketService.sendTyping(type === 'direct' ? numericId : undefined, type === 'room' ? numericId : undefined);\n    };\n    const handleSetActiveChat = (chat)=>{\n        setActiveChat(chat);\n        if (chat) {\n            loadMessages(chat.id);\n            // Mark chat as read\n            setChats((prev)=>prev.map((c)=>c.id === chat.id ? {\n                        ...c,\n                        unread_count: 0\n                    } : c));\n        }\n    };\n    const clearNotifications = ()=>{\n        setNotifications([]);\n    };\n    const markNotificationAsRead = (notificationId)=>{\n        setNotifications((prev)=>prev.map((notif)=>notif.id === notificationId ? {\n                    ...notif,\n                    read: true\n                } : notif));\n    };\n    const value = {\n        chats,\n        activeChat,\n        messages,\n        typingUsers,\n        notifications,\n        isLoading,\n        isConnected,\n        setActiveChat: handleSetActiveChat,\n        sendMessage,\n        loadMessages,\n        markAsRead,\n        sendTyping,\n        refreshChats,\n        clearNotifications,\n        markNotificationAsRead\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/time-chat/nextjs-chat/src/context/ChatContext.tsx\",\n        lineNumber: 323,\n        columnNumber: 5\n    }, undefined);\n};\nconst useChat = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ChatContext);\n    if (context === undefined) {\n        throw new Error('useChat must be used within a ChatProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/context/ChatContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiService: () => (/* binding */ apiService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\nclass ApiService {\n    constructor(){\n        this.baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';\n        this.api = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n            baseURL: this.baseURL,\n            headers: {\n                'Content-Type': 'application/json'\n            }\n        });\n        // Request interceptor to add auth token\n        this.api.interceptors.request.use((config)=>{\n            if (false) {}\n            return config;\n        }, (error)=>Promise.reject(error));\n        // Response interceptor to handle errors\n        this.api.interceptors.response.use((response)=>response, (error)=>{\n            if (error.response?.status === 401) {\n                if (false) {}\n            }\n            return Promise.reject(error);\n        });\n    }\n    // Auth endpoints\n    async login(credentials) {\n        const response = await this.api.post('/auth/login', credentials);\n        return response.data;\n    }\n    async register(userData) {\n        const response = await this.api.post('/auth/register', userData);\n        return response.data;\n    }\n    async getCurrentUser() {\n        const response = await this.api.get('/auth/me');\n        return response.data;\n    }\n    // User endpoints\n    async getUsers(page = 1, size = 50) {\n        const response = await this.api.get(`/users?page=${page}&size=${size}`);\n        return response.data;\n    }\n    async getUserById(userId) {\n        const response = await this.api.get(`/users/${userId}`);\n        return response.data;\n    }\n    async updateUser(userId, userData) {\n        const response = await this.api.put(`/users/${userId}`, userData);\n        return response.data;\n    }\n    // Message endpoints\n    async getMessages(page = 1, size = 50, recipientId, roomId) {\n        let url = `/messages?page=${page}&size=${size}`;\n        if (recipientId) url += `&recipient_id=${recipientId}`;\n        if (roomId) url += `&room_id=${roomId}`;\n        const response = await this.api.get(url);\n        return response.data;\n    }\n    async sendMessage(messageData) {\n        const response = await this.api.post('/messages', messageData);\n        return response.data;\n    }\n    async markMessageAsRead(messageId) {\n        const response = await this.api.patch(`/messages/${messageId}`, {\n            status: 'read'\n        });\n        return response.data;\n    }\n    // Room endpoints\n    async getRooms(page = 1, size = 50) {\n        const response = await this.api.get(`/rooms?page=${page}&size=${size}`);\n        return response.data;\n    }\n    async getRoomById(roomId) {\n        const response = await this.api.get(`/rooms/${roomId}`);\n        return response.data;\n    }\n    async createRoom(roomData) {\n        const response = await this.api.post('/rooms', roomData);\n        return response.data;\n    }\n    async joinRoom(roomId) {\n        await this.api.post(`/rooms/${roomId}/join`);\n    }\n    async leaveRoom(roomId) {\n        await this.api.post(`/rooms/${roomId}/leave`);\n    }\n    async getRoomMembers(roomId) {\n        const response = await this.api.get(`/rooms/${roomId}/members`);\n        return response.data;\n    }\n    // Utility methods\n    getWebSocketUrl(token) {\n        const wsBaseUrl = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000';\n        return `${wsBaseUrl}/ws?token=${encodeURIComponent(token)}`;\n    }\n}\nconst apiService = new ApiService();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatTime: () => (/* binding */ formatTime),\n/* harmony export */   generateAvatar: () => (/* binding */ generateAvatar),\n/* harmony export */   isOnline: () => (/* binding */ isOnline),\n/* harmony export */   playNotificationSound: () => (/* binding */ playNotificationSound),\n/* harmony export */   requestNotificationPermission: () => (/* binding */ requestNotificationPermission),\n/* harmony export */   showBrowserNotification: () => (/* binding */ showBrowserNotification),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatTime(dateString) {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\n    if (diffInHours < 24) {\n        return date.toLocaleTimeString('ar-SA', {\n            hour: '2-digit',\n            minute: '2-digit',\n            hour12: false\n        });\n    } else if (diffInHours < 168) {\n        return date.toLocaleDateString('ar-SA', {\n            weekday: 'short'\n        });\n    } else {\n        return date.toLocaleDateString('ar-SA', {\n            day: '2-digit',\n            month: '2-digit'\n        });\n    }\n}\nfunction formatDate(dateString) {\n    const date = new Date(dateString);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    if (date.toDateString() === today.toDateString()) {\n        return 'اليوم';\n    } else if (date.toDateString() === yesterday.toDateString()) {\n        return 'أمس';\n    } else {\n        return date.toLocaleDateString('ar-SA', {\n            weekday: 'long',\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric'\n        });\n    }\n}\nfunction truncateText(text, maxLength = 50) {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength) + '...';\n}\nfunction generateAvatar(name) {\n    const colors = [\n        '#FF6B6B',\n        '#4ECDC4',\n        '#45B7D1',\n        '#96CEB4',\n        '#FFEAA7',\n        '#DDA0DD',\n        '#98D8C8',\n        '#F7DC6F',\n        '#BB8FCE',\n        '#85C1E9'\n    ];\n    const index = name.charCodeAt(0) % colors.length;\n    return colors[index];\n}\nfunction playNotificationSound() {\n    if (false) {}\n}\nfunction requestNotificationPermission() {\n    if (false) {}\n    return Promise.resolve('denied');\n}\nfunction showBrowserNotification(title, options) {\n    if (false) {}\n}\nfunction isOnline() {\n    if (false) {}\n    return true;\n}\nfunction debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/websocket.ts":
/*!******************************!*\
  !*** ./src/lib/websocket.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   websocketService: () => (/* binding */ websocketService)\n/* harmony export */ });\nclass WebSocketService {\n    connect(token) {\n        return new Promise((resolve, reject)=>{\n            if (this.ws?.readyState === WebSocket.OPEN) {\n                resolve();\n                return;\n            }\n            if (this.isConnecting) {\n                reject(new Error('Already connecting'));\n                return;\n            }\n            this.isConnecting = true;\n            this.token = token;\n            const wsUrl = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000';\n            const fullUrl = `${wsUrl}/ws?token=${encodeURIComponent(token)}`;\n            try {\n                this.ws = new WebSocket(fullUrl);\n                this.ws.onopen = ()=>{\n                    console.log('WebSocket connected');\n                    this.isConnecting = false;\n                    this.reconnectAttempts = 0;\n                    this.emit('connected', null);\n                    resolve();\n                };\n                this.ws.onmessage = (event)=>{\n                    try {\n                        const message = JSON.parse(event.data);\n                        this.handleMessage(message);\n                    } catch (error) {\n                        console.error('Error parsing WebSocket message:', error);\n                    }\n                };\n                this.ws.onclose = (event)=>{\n                    console.log('WebSocket disconnected:', event.code, event.reason);\n                    this.isConnecting = false;\n                    this.emit('disconnected', {\n                        code: event.code,\n                        reason: event.reason\n                    });\n                    if (!event.wasClean && this.reconnectAttempts < this.maxReconnectAttempts && this.token) {\n                        this.scheduleReconnect();\n                    }\n                };\n                this.ws.onerror = (error)=>{\n                    console.error('WebSocket error:', error);\n                    this.isConnecting = false;\n                    this.emit('error', error);\n                    reject(error);\n                };\n            } catch (error) {\n                this.isConnecting = false;\n                reject(error);\n            }\n        });\n    }\n    scheduleReconnect() {\n        if (!this.token) return;\n        this.reconnectAttempts++;\n        console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);\n        setTimeout(()=>{\n            if (this.token) {\n                this.connect(this.token).catch(console.error);\n            }\n        }, this.reconnectInterval);\n    }\n    handleMessage(message) {\n        console.log('Received WebSocket message:', message);\n        switch(message.type){\n            case 'message':\n                this.emit('message', message.data);\n                break;\n            case 'user_status':\n                this.emit('userStatus', message.data);\n                break;\n            case 'typing':\n                this.emit('typing', message.data);\n                break;\n            case 'message_read':\n                this.emit('messageRead', message.data);\n                break;\n            case 'room_joined':\n                this.emit('roomJoined', message.data);\n                break;\n            case 'room_left':\n                this.emit('roomLeft', message.data);\n                break;\n            case 'notification':\n                this.emit('notification', message.data);\n                break;\n            default:\n                console.log('Unknown message type:', message.type);\n        }\n    }\n    sendMessage(content, recipientId, roomId) {\n        if (!this.isConnected()) {\n            throw new Error('WebSocket not connected');\n        }\n        const message = {\n            type: 'chat_message',\n            data: {\n                content,\n                recipient_id: recipientId,\n                room_id: roomId,\n                message_type: roomId ? 'room' : 'direct'\n            }\n        };\n        this.send(message);\n    }\n    sendTyping(recipientId, roomId) {\n        if (!this.isConnected()) return;\n        const message = {\n            type: 'typing',\n            data: {\n                recipient_id: recipientId,\n                room_id: roomId\n            }\n        };\n        this.send(message);\n    }\n    markMessageAsRead(messageId) {\n        if (!this.isConnected()) return;\n        const message = {\n            type: 'message_read',\n            data: {\n                message_id: messageId\n            }\n        };\n        this.send(message);\n    }\n    joinRoom(roomId) {\n        if (!this.isConnected()) return;\n        const message = {\n            type: 'join_room',\n            data: {\n                room_id: roomId\n            }\n        };\n        this.send(message);\n    }\n    leaveRoom(roomId) {\n        if (!this.isConnected()) return;\n        const message = {\n            type: 'leave_room',\n            data: {\n                room_id: roomId\n            }\n        };\n        this.send(message);\n    }\n    send(message) {\n        if (this.ws?.readyState === WebSocket.OPEN) {\n            this.ws.send(JSON.stringify(message));\n        }\n    }\n    isConnected() {\n        return this.ws?.readyState === WebSocket.OPEN;\n    }\n    disconnect() {\n        if (this.ws) {\n            this.ws.close();\n            this.ws = null;\n        }\n        this.token = null;\n        this.eventListeners.clear();\n    }\n    // Event system\n    on(event, callback) {\n        if (!this.eventListeners.has(event)) {\n            this.eventListeners.set(event, []);\n        }\n        this.eventListeners.get(event).push(callback);\n    }\n    off(event, callback) {\n        const listeners = this.eventListeners.get(event);\n        if (listeners) {\n            const index = listeners.indexOf(callback);\n            if (index > -1) {\n                listeners.splice(index, 1);\n            }\n        }\n    }\n    emit(event, data) {\n        const listeners = this.eventListeners.get(event);\n        if (listeners) {\n            listeners.forEach((callback)=>callback(data));\n        }\n    }\n    // Get connection status\n    getConnectionStatus() {\n        if (this.isConnecting) return 'connecting';\n        if (this.ws?.readyState === WebSocket.OPEN) return 'connected';\n        if (this.ws?.readyState === WebSocket.CLOSED) return 'disconnected';\n        return 'error';\n    }\n    constructor(){\n        this.ws = null;\n        this.reconnectAttempts = 0;\n        this.maxReconnectAttempts = 5;\n        this.reconnectInterval = 3000;\n        this.eventListeners = new Map();\n        this.isConnecting = false;\n        this.token = null;\n    }\n}\nconst websocketService = new WebSocketService();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (websocketService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/websocket.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?d272":
/*!********************************!*\
  !*** supports-color (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/axios","vendor-chunks/lucide-react","vendor-chunks/asynckit","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/call-bind-apply-helpers","vendor-chunks/debug","vendor-chunks/get-proto","vendor-chunks/mime-db","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/form-data","vendor-chunks/follow-redirects","vendor-chunks/tailwind-merge","vendor-chunks/clsx","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/mime-types","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/get-intrinsic","vendor-chunks/es-set-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/delayed-stream","vendor-chunks/combined-stream"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fmazen%2FDesktop%2Ftime-chat%2Fnextjs-chat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();